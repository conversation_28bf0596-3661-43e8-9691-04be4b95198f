// Test script for chat-assistant Edge Function
const SUPABASE_URL = 'https://lrtirloetmulgmdxnusl.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxydGlybG9ldG11bGdtZHhudXNsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5NjE4NzQsImV4cCI6MjA1MDUzNzg3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

async function testChatAssistant() {
  console.log('Testing chat-assistant Edge Function...');
  
  const testMessages = [
    {
      role: 'user',
      content: 'Show me Box Cricket availability for tomorrow'
    }
  ];
  
  const testUserId = 'fcd4f641-8498-4347-a0af-baf5d1f04ac5'; // Test user ID
  
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/chat-assistant`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: testMessages,
        userId: testUserId
      })
    });
    
    const data = await response.json();
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(data, null, 2));
    
    if (data.functionCall) {
      console.log('Function call detected:', data.functionCall.name);
      console.log('Function arguments:', data.functionCall.arguments);
      console.log('Function result success:', data.functionCall.result?.success);
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test
testChatAssistant();

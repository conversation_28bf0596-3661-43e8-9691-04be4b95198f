-- Update get_help_requests function to include user phone number
-- This script should be run manually in the Supabase SQL editor

-- Drop the existing function first
DROP FUNCTION IF EXISTS public.get_help_requests(TEXT);

-- Recreate the function with phone number included
CREATE OR REPLACE FUNCTION public.get_help_requests(p_status TEXT DEFAULT NULL)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  subject TEXT,
  status TEXT,
  ticket_number TEXT,
  category TEXT,
  venue_id UUID,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  last_message_at TIMESTAMPTZ,
  user_name TEXT,
  user_email TEXT,
  user_phone TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT
    hr.id,
    hr.user_id,
    hr.subject,
    hr.status,
    hr.ticket_number,
    hr.category,
    hr.venue_id,
    hr.created_at,
    hr.updated_at,
    hr.last_message_at,
    p.full_name AS user_name,
    p.email AS user_email,
    p.phone AS user_phone
  FROM
    public.help_requests hr
    LEFT JOIN public.profiles p ON hr.user_id = p.id
  WHERE
    (p_status IS NULL OR hr.status = p_status)
  ORDER BY
    CASE WHEN hr.status = 'open' THEN 0
         WHEN hr.status = 'in_progress' THEN 1
         ELSE 2 END,
    hr.last_message_at DESC;
END;
$$;

-- Verify the function was created successfully
SELECT 'Function get_help_requests updated successfully with phone number support' AS result;

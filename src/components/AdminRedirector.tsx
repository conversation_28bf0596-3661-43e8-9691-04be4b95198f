
import { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { useIsMobile } from '@/hooks/use-mobile';

const AdminRedirector = () => {
  const { user, userRole } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const isMobile = useIsMobile();

  useEffect(() => {
    if (user && (userRole === 'admin' || userRole === 'super_admin')) {
      // Don't redirect if already on a valid admin route (including mobile routes)
      if (location.pathname.startsWith('/admin/') && location.pathname !== '/admin/') {
        return;
      }

      // Redirect admin users from any non-admin route to appropriate admin page
      if (!location.pathname.startsWith('/admin')) {
        if (isMobile) {
          navigate('/admin/mobile-home', { replace: true });
        } else {
          navigate('/admin#dashboard', { replace: true });
        }
        return;
      }

      // If the admin is at exactly /admin without a hash, add the dashboard hash
      if (location.pathname === '/admin' && !location.hash) {
        navigate('/admin#dashboard', { replace: true });
      }
    }
  }, [user, userRole, location, navigate, isMobile]);

  return null;
};

export default AdminRedirector;

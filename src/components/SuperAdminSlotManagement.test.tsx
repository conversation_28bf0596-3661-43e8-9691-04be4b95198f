import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import SuperAdminSlotManagement from '@/components/admin/SuperAdminSlotManagement';

// Mock the hooks
vi.mock('@/hooks/useSlotManagement', () => ({
  useSlotManagement: () => ({
    blockedSlots: [
      {
        id: 'slot-1',
        court_id: 'court-1',
        court_name: 'Court 1',
        venue_id: 'venue-1',
        venue_name: 'Test Venue',
        date: '2024-01-15',
        start_time: '09:00',
        end_time: '10:00',
        reason: 'Maintenance',
        created_by: 'admin-1',
        created_by_name: 'Admin User',
        created_at: '2024-01-14T10:00:00Z'
      }
    ],
    stats: {
      total_blocked_slots: 5,
      venues_with_blocks: 2,
      courts_with_blocks: 3,
      date_range: {
        start_date: '2024-01-15',
        end_date: '2024-01-22'
      }
    },
    loading: false,
    error: null,
    fetchBlockedSlots: vi.fn(),
    fetchFilteredBlockedSlots: vi.fn(),
    fetchStats: vi.fn(),
    bulkBlockSlots: vi.fn().mockResolvedValue(true),
    unblockSlots: vi.fn().mockResolvedValue(true),
    clearError: vi.fn()
  })
}));

vi.mock('@/services/slotManagementService', () => ({
  default: {
    getVenuesWithCourts: vi.fn().mockResolvedValue({
      data: [
        {
          id: 'venue-1',
          name: 'Test Venue',
          location: 'Test Location',
          courts: [
            { id: 'court-1', name: 'Court 1', venue_id: 'venue-1' },
            { id: 'court-2', name: 'Court 2', venue_id: 'venue-1' }
          ]
        }
      ],
      error: null
    })
  }
}));

const renderComponent = () => {
  return render(
    <BrowserRouter>
      <SuperAdminSlotManagement />
    </BrowserRouter>
  );
};

describe('SuperAdminSlotManagement', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the main interface with stats', async () => {
    renderComponent();

    // Check if main title is rendered
    expect(screen.getByText('Super Admin Slot Management')).toBeInTheDocument();

    // Check if stats cards are rendered
    await waitFor(() => {
      expect(screen.getByText('Total Blocked Slots')).toBeInTheDocument();
      expect(screen.getByText('5')).toBeInTheDocument();
      expect(screen.getByText('Venues Affected')).toBeInTheDocument();
      expect(screen.getByText('2')).toBeInTheDocument();
    });
  });

  it('displays the tab navigation', () => {
    renderComponent();

    expect(screen.getByRole('tab', { name: /overview/i })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: /manage slots/i })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: /bulk operations/i })).toBeInTheDocument();
  });

  it('shows blocked slots in the manage tab', async () => {
    renderComponent();

    // Click on manage tab
    fireEvent.click(screen.getByRole('tab', { name: /manage slots/i }));

    await waitFor(() => {
      expect(screen.getByText('Test Venue')).toBeInTheDocument();
      expect(screen.getByText('Court 1')).toBeInTheDocument();
      expect(screen.getByText('Maintenance')).toBeInTheDocument();
    });
  });

  it('allows searching and filtering', async () => {
    renderComponent();

    // Click on manage tab
    fireEvent.click(screen.getByRole('tab', { name: /manage slots/i }));

    // Find and interact with search input
    const searchInput = screen.getByPlaceholderText(/search venues, courts, reasons/i);
    expect(searchInput).toBeInTheDocument();

    fireEvent.change(searchInput, { target: { value: 'maintenance' } });
    expect(searchInput).toHaveValue('maintenance');
  });

  it('shows bulk operations form', async () => {
    renderComponent();

    // Click on bulk operations tab
    fireEvent.click(screen.getByRole('tab', { name: /bulk operations/i }));

    await waitFor(() => {
      expect(screen.getByText('Bulk Slot Blocking')).toBeInTheDocument();
      expect(screen.getByText('Select Venue')).toBeInTheDocument();
    });
  });

  it('handles venue selection in bulk operations', async () => {
    renderComponent();

    // Click on bulk operations tab
    fireEvent.click(screen.getByRole('tab', { name: /bulk operations/i }));

    await waitFor(() => {
      const venueSelect = screen.getByText('Choose a venue');
      expect(venueSelect).toBeInTheDocument();
    });
  });

  it('displays real-time status indicator', () => {
    renderComponent();

    expect(screen.getByText('Real-time Updates Active')).toBeInTheDocument();
  });

  it('shows advanced filters when toggled', async () => {
    renderComponent();

    // Click on manage tab
    fireEvent.click(screen.getByRole('tab', { name: /manage slots/i }));

    // Click advanced filters button
    const advancedButton = screen.getByText('Advanced Filters');
    fireEvent.click(advancedButton);

    await waitFor(() => {
      expect(screen.getByText('Specific Court')).toBeInTheDocument();
      expect(screen.getByText('Reason Contains')).toBeInTheDocument();
      expect(screen.getByText('Created By Admin')).toBeInTheDocument();
    });
  });

  it('handles date preset changes', async () => {
    renderComponent();

    // Click on manage tab
    fireEvent.click(screen.getByRole('tab', { name: /manage slots/i }));

    // Find date preset selector
    const datePresetSelect = screen.getByDisplayValue('This Week');
    expect(datePresetSelect).toBeInTheDocument();
  });

  it('shows slot selection and bulk unblock functionality', async () => {
    renderComponent();

    // Click on manage tab
    fireEvent.click(screen.getByRole('tab', { name: /manage slots/i }));

    await waitFor(() => {
      // Check if slot checkboxes are present
      const checkboxes = screen.getAllByRole('checkbox');
      expect(checkboxes.length).toBeGreaterThan(0);

      // Check if "Select All" option is present
      expect(screen.getByText('Select All')).toBeInTheDocument();
    });
  });

  it('displays notification component', () => {
    renderComponent();

    // The notification bell should be present in the header
    const notificationElements = screen.getAllByRole('button');
    expect(notificationElements.length).toBeGreaterThan(0);
  });

  it('handles error states gracefully', async () => {
    // Mock error state
    vi.mocked(vi.importMock('@/hooks/useSlotManagement')).useSlotManagement.mockReturnValue({
      blockedSlots: [],
      stats: null,
      loading: false,
      error: 'Failed to fetch data',
      fetchBlockedSlots: vi.fn(),
      fetchFilteredBlockedSlots: vi.fn(),
      fetchStats: vi.fn(),
      bulkBlockSlots: vi.fn(),
      unblockSlots: vi.fn(),
      clearError: vi.fn()
    });

    renderComponent();

    // Component should still render without crashing
    expect(screen.getByText('Super Admin Slot Management')).toBeInTheDocument();
  });

  it('shows loading state', async () => {
    // Mock loading state
    vi.mocked(vi.importMock('@/hooks/useSlotManagement')).useSlotManagement.mockReturnValue({
      blockedSlots: [],
      stats: null,
      loading: true,
      error: null,
      fetchBlockedSlots: vi.fn(),
      fetchFilteredBlockedSlots: vi.fn(),
      fetchStats: vi.fn(),
      bulkBlockSlots: vi.fn(),
      unblockSlots: vi.fn(),
      clearError: vi.fn()
    });

    renderComponent();

    // Should show loading spinner
    expect(screen.getByRole('status') || screen.getByTestId('loading')).toBeInTheDocument();
  });
});

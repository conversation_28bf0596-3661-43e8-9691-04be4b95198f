import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Plus, Edit, Trash2, Eye, EyeOff, Calendar, Users, TrendingUp } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { toast } from "react-hot-toast";

interface Coupon {
  id: string;
  code: string;
  discount_type: 'percentage' | 'flat';
  discount_value: number;
  venue_id: string | null;
  venue_name: string | null;
  max_uses: number | null;
  current_uses: number;
  valid_until: string | null;
  is_public: boolean;
  is_active: boolean;
  created_at: string;
}

interface CouponAnalytics {
  coupon_id: string;
  code: string;
  total_uses: number;
  total_discount_given: number;
  unique_users: number;
  last_used: string | null;
}

export function CouponManagement() {
  const [coupons, setCoupons] = useState<Coupon[]>([]);
  const [analytics, setAnalytics] = useState<CouponAnalytics[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showAnalytics, setShowAnalytics] = useState(false);

  // Form state for creating new coupons
  const [formData, setFormData] = useState({
    code: '',
    discount_type: 'percentage' as 'percentage' | 'flat',
    discount_value: '',
    venue_id: '',
    max_uses: '',
    valid_until: '',
    is_public: false
  });

  useEffect(() => {
    fetchCoupons();
    if (showAnalytics) {
      fetchAnalytics();
    }
  }, [showAnalytics]);

  const fetchCoupons = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('coupons')
        .select(`
          *,
          venues(name)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const formattedCoupons = data?.map(coupon => ({
        ...coupon,
        venue_name: coupon.venues?.name || null
      })) || [];

      setCoupons(formattedCoupons);
    } catch (error) {
      console.error('Error fetching coupons:', error);
      toast.error('Failed to fetch coupons');
    } finally {
      setLoading(false);
    }
  };

  const fetchAnalytics = async () => {
    try {
      const { data, error } = await supabase
        .rpc('get_coupon_analytics');

      if (error) throw error;
      setAnalytics(data || []);
    } catch (error) {
      console.error('Error fetching analytics:', error);
      toast.error('Failed to fetch analytics');
    }
  };

  const createCoupon = async () => {
    try {
      const { data, error } = await supabase
        .rpc('create_coupon', {
          p_code: formData.code,
          p_discount_type: formData.discount_type,
          p_discount_value: parseFloat(formData.discount_value),
          p_venue_id: formData.venue_id || null,
          p_max_uses: formData.max_uses ? parseInt(formData.max_uses) : null,
          p_valid_until: formData.valid_until || null,
          p_is_public: formData.is_public
        });

      if (error) throw error;

      if (!data.success) {
        throw new Error(data.error);
      }

      toast.success('Coupon created successfully');
      setShowCreateModal(false);
      setFormData({
        code: '',
        discount_type: 'percentage',
        discount_value: '',
        venue_id: '',
        max_uses: '',
        valid_until: '',
        is_public: false
      });
      fetchCoupons();
    } catch (error: any) {
      console.error('Error creating coupon:', error);
      toast.error(error.message || 'Failed to create coupon');
    }
  };

  const toggleCouponStatus = async (couponId: string, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('coupons')
        .update({ is_active: !currentStatus })
        .eq('id', couponId);

      if (error) throw error;

      toast.success(`Coupon ${!currentStatus ? 'activated' : 'deactivated'}`);
      fetchCoupons();
    } catch (error) {
      console.error('Error updating coupon:', error);
      toast.error('Failed to update coupon');
    }
  };

  const formatDiscount = (type: string, value: number) => {
    return type === 'percentage' ? `${value}%` : `₹${value}`;
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'No expiry';
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-emerald-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white">Coupon Management</h2>
        <div className="flex gap-2">
          <Button
            onClick={() => setShowAnalytics(!showAnalytics)}
            variant="outline"
            className="bg-gray-800 border-gray-600 text-gray-200"
          >
            <TrendingUp className="w-4 h-4 mr-2" />
            {showAnalytics ? 'Hide' : 'Show'} Analytics
          </Button>
          <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
            <DialogTrigger asChild>
              <Button className="bg-emerald-600 hover:bg-emerald-700">
                <Plus className="w-4 h-4 mr-2" />
                Create Coupon
              </Button>
            </DialogTrigger>
            <DialogContent className="bg-gray-900 border-gray-700 text-white">
              <DialogHeader>
                <DialogTitle>Create New Coupon</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Coupon Code</label>
                  <input
                    type="text"
                    value={formData.code}
                    onChange={(e) => setFormData({...formData, code: e.target.value.toUpperCase()})}
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white"
                    placeholder="e.g., SAVE20"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Discount Type</label>
                    <select
                      value={formData.discount_type}
                      onChange={(e) => setFormData({...formData, discount_type: e.target.value as 'percentage' | 'flat'})}
                      className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white"
                    >
                      <option value="percentage">Percentage</option>
                      <option value="flat">Flat Amount</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Discount Value {formData.discount_type === 'percentage' ? '(%)' : '(₹)'}
                    </label>
                    <input
                      type="number"
                      value={formData.discount_value}
                      onChange={(e) => setFormData({...formData, discount_value: e.target.value})}
                      className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white"
                      placeholder={formData.discount_type === 'percentage' ? '20' : '100'}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Max Uses (optional)</label>
                    <input
                      type="number"
                      value={formData.max_uses}
                      onChange={(e) => setFormData({...formData, max_uses: e.target.value})}
                      className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white"
                      placeholder="Unlimited"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-1">Valid Until (optional)</label>
                    <input
                      type="datetime-local"
                      value={formData.valid_until}
                      onChange={(e) => setFormData({...formData, valid_until: e.target.value})}
                      className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white"
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="is_public"
                    checked={formData.is_public}
                    onChange={(e) => setFormData({...formData, is_public: e.target.checked})}
                    className="rounded border-gray-600"
                  />
                  <label htmlFor="is_public" className="text-sm">
                    Show publicly on venue pages
                  </label>
                </div>

                <Button onClick={createCoupon} className="w-full bg-emerald-600 hover:bg-emerald-700">
                  Create Coupon
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Analytics Section */}
      {showAnalytics && (
        <Card className="bg-gray-900 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white">Coupon Analytics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="bg-emerald-900/20 p-4 rounded-lg">
                <div className="text-emerald-400 text-sm">Total Active Coupons</div>
                <div className="text-2xl font-bold text-white">
                  {coupons.filter(c => c.is_active).length}
                </div>
              </div>
              <div className="bg-blue-900/20 p-4 rounded-lg">
                <div className="text-blue-400 text-sm">Total Uses</div>
                <div className="text-2xl font-bold text-white">
                  {analytics.reduce((sum, a) => sum + a.total_uses, 0)}
                </div>
              </div>
              <div className="bg-purple-900/20 p-4 rounded-lg">
                <div className="text-purple-400 text-sm">Total Discount Given</div>
                <div className="text-2xl font-bold text-white">
                  ₹{analytics.reduce((sum, a) => sum + a.total_discount_given, 0).toFixed(2)}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Coupons List */}
      <div className="grid gap-4">
        {coupons.map((coupon) => (
          <Card key={coupon.id} className="bg-gray-900 border-gray-700">
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <span className="text-lg font-bold text-emerald-400 font-mono">
                      {coupon.code}
                    </span>
                    <span className="bg-emerald-600 text-white px-2 py-1 rounded text-sm">
                      {formatDiscount(coupon.discount_type, coupon.discount_value)} OFF
                    </span>
                    {coupon.is_public && (
                      <span className="bg-blue-600 text-white px-2 py-1 rounded text-xs">
                        PUBLIC
                      </span>
                    )}
                    <span className={`px-2 py-1 rounded text-xs ${
                      coupon.is_active ? 'bg-green-600 text-white' : 'bg-red-600 text-white'
                    }`}>
                      {coupon.is_active ? 'ACTIVE' : 'INACTIVE'}
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-300">
                    <div>
                      <span className="text-gray-400">Venue:</span>
                      <div>{coupon.venue_name || 'All Venues'}</div>
                    </div>
                    <div>
                      <span className="text-gray-400">Usage:</span>
                      <div>
                        {coupon.current_uses}
                        {coupon.max_uses ? ` / ${coupon.max_uses}` : ' / Unlimited'}
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-400">Expires:</span>
                      <div>{formatDate(coupon.valid_until)}</div>
                    </div>
                    <div>
                      <span className="text-gray-400">Created:</span>
                      <div>{formatDate(coupon.created_at)}</div>
                    </div>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => toggleCouponStatus(coupon.id, coupon.is_active)}
                    className="bg-gray-800 border-gray-600 text-gray-200"
                  >
                    {coupon.is_active ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {coupons.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">No coupons found</div>
          <Button onClick={() => setShowCreateModal(true)} className="bg-emerald-600 hover:bg-emerald-700">
            Create Your First Coupon
          </Button>
        </div>
      )}
    </div>
  );
}

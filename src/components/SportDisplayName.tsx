
import React, { useEffect, useState } from 'react';
import { getVenueSportDisplayNames } from '@/utils/sportDisplayNames';
import { supabase } from '@/integrations/supabase/client';

interface SportDisplayNameProps {
  venueId: string;
  sportId: string;
  defaultName: string;
}

const SportDisplayName: React.FC<SportDisplayNameProps> = ({ venueId, sportId, defaultName }) => {
  const [displayName, setDisplayName] = useState<string>(defaultName);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDisplayName = async () => {
      try {
        setLoading(true);

        // First check if it's a capacity-based sport
        const { data: sportData, error: sportError } = await supabase
          .from('sports')
          .select('booking_type')
          .eq('id', sportId)
          .single();

        if (sportError) throw sportError;

        // Get custom display name from venue_sport_display_names
        const displayNames = await getVenueSportDisplayNames(venueId);
        let finalDisplayName = displayNames[sportId] || defaultName;

        // For capacity-based sports, append "(Per Person)" if not already in the name
        if (sportData?.booking_type === 'capacity_based' && !finalDisplayName.includes('(Per Person)')) {
          finalDisplayName = `${finalDisplayName} (Per Person)`;
        }

        setDisplayName(finalDisplayName);
      } catch (error) {
        console.error('Error fetching sport display name:', error);
        setDisplayName(defaultName);
      } finally {
        setLoading(false);
      }
    };

    if (venueId && sportId) {
      fetchDisplayName();
    }
  }, [venueId, sportId, defaultName]);

  if (loading) {
    return <span>{defaultName}</span>;
  }

  return <>{displayName}</>;
};

export default SportDisplayName;

import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Tag, Clock, Users, Copy, Check } from 'lucide-react';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "react-hot-toast";

interface Coupon {
  id: string;
  code: string;
  discount_type: 'percentage' | 'flat';
  discount_value: number;
  max_uses: number | null;
  current_uses: number;
  valid_until: string | null;
  usage_remaining: number | null;
}

interface VenueCouponsProps {
  venueId: string;
  className?: string;
}

export function VenueCoupons({ venueId, className = "" }: VenueCouponsProps) {
  const [coupons, setCoupons] = useState<Coupon[]>([]);
  const [loading, setLoading] = useState(true);
  const [copiedCode, setCopiedCode] = useState<string | null>(null);

  useEffect(() => {
    fetchCoupons();
  }, [venueId]);

  const fetchCoupons = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .rpc('get_venue_public_coupons', { p_venue_id: venueId });
        
      if (error) throw error;
      
      setCoupons(data || []);
    } catch (error) {
      console.error('Error fetching coupons:', error);
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code);
      setCopiedCode(code);
      toast.success(`Coupon code "${code}" copied!`);
      
      // Reset copied state after 2 seconds
      setTimeout(() => setCopiedCode(null), 2000);
    } catch (error) {
      toast.error('Failed to copy coupon code');
    }
  };

  const formatDiscount = (type: string, value: number) => {
    if (type === 'percentage') {
      return `${value}% OFF`;
    } else {
      return `₹${value} OFF`;
    }
  };

  const formatExpiry = (validUntil: string | null) => {
    if (!validUntil) return 'No expiry';
    
    const expiryDate = new Date(validUntil);
    const now = new Date();
    const diffTime = expiryDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays <= 0) return 'Expired';
    if (diffDays === 1) return 'Expires today';
    if (diffDays <= 7) return `Expires in ${diffDays} days`;
    
    return expiryDate.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: expiryDate.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    });
  };

  if (loading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-6 bg-emerald-900/20 rounded mb-3 w-32"></div>
        <div className="space-y-3">
          <div className="h-20 bg-emerald-900/10 rounded"></div>
          <div className="h-20 bg-emerald-900/10 rounded"></div>
        </div>
      </div>
    );
  }

  if (coupons.length === 0) {
    return null; // Don't show anything if no coupons
  }

  return (
    <div className={className}>
      <h3 className="text-lg font-bold text-emerald-400 mb-3 flex items-center">
        <Tag className="w-5 h-5 mr-2" />
        Available Offers
      </h3>
      
      <div className="space-y-3">
        {coupons.map((coupon) => (
          <Card 
            key={coupon.id} 
            className="bg-gradient-to-r from-emerald-900/20 to-emerald-800/10 border-emerald-700/30 hover:border-emerald-600/50 transition-all duration-200"
          >
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <div className="bg-emerald-600 text-white px-3 py-1 rounded-full text-sm font-bold">
                      {formatDiscount(coupon.discount_type, coupon.discount_value)}
                    </div>
                    <div className="flex items-center text-xs text-gray-400">
                      <Clock className="w-3 h-3 mr-1" />
                      {formatExpiry(coupon.valid_until)}
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-emerald-300 font-mono text-sm bg-black/30 px-2 py-1 rounded">
                        {coupon.code}
                      </span>
                      {coupon.usage_remaining !== null && (
                        <div className="flex items-center text-xs text-gray-400">
                          <Users className="w-3 h-3 mr-1" />
                          {coupon.usage_remaining} left
                        </div>
                      )}
                    </div>
                    
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(coupon.code)}
                      className="bg-emerald-900/30 border-emerald-700/50 text-emerald-300 hover:bg-emerald-800/40 hover:text-emerald-200 h-8 px-3"
                    >
                      {copiedCode === coupon.code ? (
                        <Check className="w-3 h-3" />
                      ) : (
                        <Copy className="w-3 h-3" />
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      
      <p className="text-xs text-gray-500 mt-3 text-center">
        Copy the code and apply it during booking to get the discount
      </p>
    </div>
  );
}

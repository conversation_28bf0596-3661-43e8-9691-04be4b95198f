import React, { useState } from 'react';
import { X, Phone, Key, Lock, Eye, EyeOff, MessageCircle } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { validatePhone, sanitizeInput } from '@/utils/security';
import { supabase } from '@/integrations/supabase/client';

interface ForgotPasswordModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ForgotPasswordModal: React.FC<ForgotPasswordModalProps> = ({ isOpen, onClose }) => {
  const [step, setStep] = useState<'phone' | 'otp' | 'password'>('phone');
  const [phone, setPhone] = useState('');
  const [countryCode, setCountryCode] = useState('+91');
  const [otp, setOtp] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [timer, setTimer] = useState(0);

  // Start OTP timer
  const startTimer = () => {
    setTimer(300); // 5 minutes
    const interval = setInterval(() => {
      setTimer((prev) => {
        if (prev <= 1) {
          clearInterval(interval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // Format timer display
  const formatTimer = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Reset modal state
  const resetModal = () => {
    setStep('phone');
    setPhone('');
    setOtp('');
    setNewPassword('');
    setConfirmPassword('');
    setShowPassword(false);
    setShowConfirmPassword(false);
    setIsLoading(false);
    setTimer(0);
  };

  // Handle close
  const handleClose = () => {
    resetModal();
    onClose();
  };

  // Send OTP for password reset
  const handleSendOTP = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!phone || !validatePhone(countryCode + phone)) {
      toast({
        title: "Invalid phone number",
        description: "Please enter a valid phone number.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      const fullPhone = countryCode + sanitizeInput(phone);

      const { data, error } = await supabase.functions.invoke('forgot-password-sms', {
        body: {
          phone: fullPhone,
          action: 'send-otp'
        }
      });

      if (error) {
        console.error('Send OTP error:', error);
        toast({
          title: "Failed to send OTP",
          description: "Please try again.",
          variant: "destructive",
        });
        return;
      }

      if (data.success) {
        setStep('otp');
        startTimer();
        toast({
          title: "OTP Sent!",
          description: `We've sent a password reset OTP to your phone number ${fullPhone}`,
        });
      } else {
        toast({
          title: "Failed to send OTP",
          description: data.error || "Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Send OTP error:', error);
      toast({
        title: "Failed to send OTP",
        description: "Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Verify OTP
  const handleVerifyOTP = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!otp || otp.length !== 6) {
      toast({
        title: "Invalid OTP",
        description: "Please enter a valid 6-digit OTP.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      const fullPhone = countryCode + sanitizeInput(phone);

      const { data, error } = await supabase.functions.invoke('forgot-password-sms', {
        body: {
          phone: fullPhone,
          otp: otp,
          action: 'verify-otp'
        }
      });

      if (error) {
        console.error('Verify OTP error:', error);
        toast({
          title: "OTP verification failed",
          description: "Please try again.",
          variant: "destructive",
        });
        return;
      }

      if (data.success) {
        setStep('password');
        toast({
          title: "OTP verified!",
          description: "Please set your new password.",
        });
      } else {
        toast({
          title: "OTP verification failed",
          description: data.error || "Please check your OTP and try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Verify OTP error:', error);
      toast({
        title: "OTP verification failed",
        description: "Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Reset password
  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newPassword || newPassword.length < 6) {
      toast({
        title: "Invalid password",
        description: "Password must be at least 6 characters long.",
        variant: "destructive",
      });
      return;
    }

    if (newPassword !== confirmPassword) {
      toast({
        title: "Passwords don't match",
        description: "Please make sure both passwords match.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      const fullPhone = countryCode + sanitizeInput(phone);

      const { data, error } = await supabase.functions.invoke('forgot-password-sms', {
        body: {
          phone: fullPhone,
          otp: otp,
          newPassword: newPassword,
          action: 'reset-password'
        }
      });

      if (error) {
        console.error('Reset password error:', error);
        toast({
          title: "Password reset failed",
          description: "Please try again.",
          variant: "destructive",
        });
        return;
      }

      if (data.success) {
        toast({
          title: "Password reset successful!",
          description: "You can now login with your new password.",
        });
        handleClose();
      } else {
        toast({
          title: "Password reset failed",
          description: data.error || "Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Reset password error:', error);
      toast({
        title: "Password reset failed",
        description: "Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-black/90 border-2 border-[#1E3B2C]/60 rounded-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-[#2E7D32]">
              {step === 'phone' && 'Reset Password'}
              {step === 'otp' && 'Verify OTP'}
              {step === 'password' && 'New Password'}
            </h2>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* Step 1: Phone Number */}
          {step === 'phone' && (
            <form onSubmit={handleSendOTP} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-green-400 mb-2">
                  Phone Number
                </label>
                <div className="flex gap-2">
                  <select
                    value={countryCode}
                    onChange={(e) => setCountryCode(e.target.value)}
                    className="w-20 p-3 border border-green-500/30 bg-black/70 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-green-400 transition-all text-sm"
                  >
                    <option value="+91">🇮🇳 +91</option>
                    <option value="+1">🇺🇸 +1</option>
                    <option value="+44">🇬🇧 +44</option>
                    <option value="+971">🇦🇪 +971</option>
                    <option value="+65">🇸🇬 +65</option>
                  </select>
                  <div className="relative flex-1">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Phone className="h-5 w-5 text-green-400" />
                    </div>
                    <input
                      type="tel"
                      value={phone}
                      onChange={(e) => setPhone(e.target.value)}
                      className="pl-10 w-full p-3 border border-green-500/30 bg-black/70 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-green-400 transition-all"
                      placeholder="Enter your phone number"
                      required
                      maxLength={15}
                    />
                  </div>
                </div>
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full py-3 px-4 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white rounded-md font-bold transition-all flex justify-center items-center disabled:opacity-50"
              >
                {isLoading ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Sending OTP...
                  </span>
                ) : (
                  <span className="flex items-center gap-2">
                    <MessageCircle className="h-4 w-4" />
                    Send SMS OTP
                  </span>
                )}
              </button>
            </form>
          )}

          {/* Step 2: OTP Verification */}
          {step === 'otp' && (
            <form onSubmit={handleVerifyOTP} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-green-400 mb-2">
                  Enter OTP
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Key className="h-5 w-5 text-green-400" />
                  </div>
                  <input
                    type="text"
                    value={otp}
                    onChange={(e) => {
                      const value = e.target.value.replace(/\D/g, '').slice(0, 6);
                      setOtp(value);
                    }}
                    className="pl-10 w-full p-3 border border-green-500/30 bg-black/70 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-green-400 transition-all text-center text-xl tracking-widest"
                    placeholder="000000"
                    required
                    maxLength={6}
                  />
                </div>
                <div className="flex justify-between items-center mt-2">
                  <p className="text-green-400 text-sm">
                    📱 OTP sent to {countryCode}{phone}
                  </p>
                  {timer > 0 ? (
                    <p className="text-gray-400 text-sm">
                      Resend in {formatTimer(timer)}
                    </p>
                  ) : (
                    <button
                      type="button"
                      onClick={() => setStep('phone')}
                      className="text-green-400 text-sm hover:text-green-300 transition-colors"
                    >
                      Change Number
                    </button>
                  )}
                </div>
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full py-3 px-4 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white rounded-md font-bold transition-all flex justify-center items-center disabled:opacity-50"
              >
                {isLoading ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Verifying...
                  </span>
                ) : (
                  <span className="flex items-center gap-2">
                    <Key className="h-4 w-4" />
                    Verify OTP
                  </span>
                )}
              </button>
            </form>
          )}

          {/* Step 3: New Password */}
          {step === 'password' && (
            <form onSubmit={handleResetPassword} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-green-400 mb-2">
                  New Password
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-green-400" />
                  </div>
                  <input
                    type={showPassword ? "text" : "password"}
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    className="pl-10 pr-10 w-full p-3 border border-green-500/30 bg-black/70 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-green-400 transition-all"
                    placeholder="Enter new password"
                    required
                    minLength={6}
                    maxLength={128}
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="text-green-400 hover:text-white focus:outline-none"
                    >
                      {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </button>
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-green-400 mb-2">
                  Confirm Password
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-green-400" />
                  </div>
                  <input
                    type={showConfirmPassword ? "text" : "password"}
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className="pl-10 pr-10 w-full p-3 border border-green-500/30 bg-black/70 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-green-400 transition-all"
                    placeholder="Confirm new password"
                    required
                    minLength={6}
                    maxLength={128}
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="text-green-400 hover:text-white focus:outline-none"
                    >
                      {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </button>
                  </div>
                </div>
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full py-3 px-4 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white rounded-md font-bold transition-all flex justify-center items-center disabled:opacity-50"
              >
                {isLoading ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Resetting Password...
                  </span>
                ) : (
                  <span className="flex items-center gap-2">
                    <Lock className="h-4 w-4" />
                    Reset Password
                  </span>
                )}
              </button>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default ForgotPasswordModal;

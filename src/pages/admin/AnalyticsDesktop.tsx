import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { CalendarIcon, UsersIcon } from 'lucide-react';
import { format, startOfMonth, endOfMonth, subMonths, parseISO, isWithinInterval, addDays } from 'date-fns';
import { toast } from '@/components/ui/use-toast';
import { useAuth } from '@/context/AuthContext';
import PaymentMethodFilter, { PaymentMethodFilterType } from '@/components/admin/PaymentMethodFilter';
import { ResponsiveContainer, LineChart as RechartsLineChart, Line, XAxis, YAxis, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> as Recharts<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'recharts';

interface BookingData {
  id: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  total_price: number;
  status: string;
  payment_method: string;
  court: {
    name: string;
    venue_id: string;
    sport_id: string;
    sports: {
      name: string;
    };
    venues: {
      name?: string;
    };
    platform_fee_percentage: number;
  };
}

interface AnalyticsDesktopProps {
  userRole: string | null;
  adminVenues: Array<{ venue_id: string }>;
}

const AnalyticsDesktop: React.FC<AnalyticsDesktopProps> = ({ userRole, adminVenues }) => {
  const [loading, setLoading] = useState(true);
  const [bookings, setBookings] = useState<BookingData[]>([]);
  const [timeRange, setTimeRange] = useState('month');
  const [selectedVenueId, setSelectedVenueId] = useState<string>('all');
  const [venues, setVenues] = useState<Array<{ id: string, name: string }>>([]);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [paymentMethodFilter, setPaymentMethodFilter] = useState<PaymentMethodFilterType>('online');
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const { data: venuesData, error: venuesError } = await supabase
          .from('venues')
          .select('id, name, platform_fee_percentage')
          .eq('is_active', true);
        if (venuesError) throw venuesError;

        // Filter out error objects from venuesData
        const validVenuesData = (venuesData || []).filter((v: any) => v && typeof v === 'object' && 'id' in v && 'name' in v && 'platform_fee_percentage' in v);
        setVenues(validVenuesData.map((v: any) => ({ id: v.id, name: v.name })));

        // Get venue IDs that this admin manages
        const venueIds = adminVenues.map(v => v.venue_id);
        
        if (venueIds.length === 0 && userRole === 'admin') {
          setBookings([]);
          setLoading(false);
          return;
        }

        // Fetch bookings with proper joins to get sport data
        let query = supabase
          .from('bookings')
          .select(`
            id,
            booking_date,
            start_time,
            end_time,
            total_price,
            payment_method,
            status,
            created_at,
            court:courts!inner (
              name,
              venue_id,
              sport_id,
              venue:venues!inner (
                id,
                name,
                platform_fee_percentage
              ),
              sport:sports (
                id,
                name
              )
            )
          `)
          .in('status', ['confirmed', 'completed']);

        // Filter by admin venues if not super_admin
        if (userRole === 'admin' && venueIds.length > 0) {
          query = query.in('court.venue.id', venueIds);
        }

        const { data: bookingsData, error: bookingsError } = await query;

        if (bookingsError) throw bookingsError;

        // Transform data to include venue_name and sport_name for easier access
        const processedBookings = (bookingsData || []).map(booking => ({
          ...booking,
          court: {
            name: booking.court?.name || '',
            venue_id: booking.court?.venue?.id || '',
            sport_id: booking.court?.sport_id || '',
            sports: {
              name: booking.court?.sport?.name || 'Unknown Sport'
            },
            venues: {
              name: booking.court?.venue?.name || 'Unknown Venue'
            },
            platform_fee_percentage: booking.court?.venue?.platform_fee_percentage ?? 5
          }
        })) as BookingData[];

        setBookings(processedBookings);
      } catch (error) {
        console.error('Error fetching analytics data:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch analytics data',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };
    
    if (adminVenues.length > 0 || userRole === 'super_admin') {
      fetchData();
    }
  }, [adminVenues, userRole]);

  useEffect(() => {
    if (userRole === 'admin' && adminVenues.length > 0) {
      setSelectedVenueId(adminVenues[0].venue_id);
    }
  }, [userRole, adminVenues]);

  const getPaymentMethodFilteredBookings = (bookings: BookingData[]) => {
    if (paymentMethodFilter === 'all') return bookings;
    
    return bookings.filter(booking => {
      const method = booking.payment_method || 'online';
      if (paymentMethodFilter === 'online') {
        return method === 'online';
      } else {
        return method === 'cash' || method === 'card';
      }
    });
  };

  const filteredBookings = getPaymentMethodFilteredBookings(
    bookings.filter(booking => {
      const bookingDate = parseISO(booking.booking_date);
      let rangeStart, rangeEnd;
      switch(timeRange) {
        case 'week':
          rangeStart = addDays(currentDate, -7);
          rangeEnd = currentDate;
          break;
        case 'month':
          rangeStart = startOfMonth(currentDate);
          rangeEnd = endOfMonth(currentDate);
          break;
        case 'year':
          rangeStart = new Date(currentDate.getFullYear(), 0, 1);
          rangeEnd = new Date(currentDate.getFullYear(), 11, 31);
          break;
        default:
          rangeStart = startOfMonth(currentDate);
          rangeEnd = endOfMonth(currentDate);
      }
      const isInDateRange = isWithinInterval(bookingDate, { start: rangeStart, end: rangeEnd });
      const isMatchingVenue = selectedVenueId === 'all' || booking.court?.venue_id === selectedVenueId;
      return isInDateRange && isMatchingVenue;
    })
  );

  const totalRevenue = filteredBookings.reduce((sum, booking) => sum + booking.total_price, 0);
  const totalPlatformFee = filteredBookings.reduce((sum, booking) => {
    const feePercent = booking.court?.platform_fee_percentage ?? 5;
    return sum + (booking.total_price * (feePercent / 100));
  }, 0);
  const totalNetRevenue = totalRevenue - totalPlatformFee;

  // Generate booking trends data
  const generateBookingTrends = () => {
    let rangeStart: Date, rangeEnd: Date;

    if (timeRange === 'week') {
      rangeStart = addDays(currentDate, -7);
      rangeEnd = currentDate;
    } else if (timeRange === 'month') {
      rangeStart = startOfMonth(currentDate);
      rangeEnd = endOfMonth(currentDate);
    } else {
      rangeStart = new Date(currentDate.getFullYear(), 0, 1);
      rangeEnd = new Date(currentDate.getFullYear(), 11, 31);
    }

    const dateMap: Record<string, { count: number; revenue: number }> = {};

    filteredBookings.forEach(booking => {
      const bookingDate = parseISO(booking.booking_date);
      if (isWithinInterval(bookingDate, { start: rangeStart, end: rangeEnd })) {
        const dateKey = format(bookingDate, 'MMM dd');
        if (!dateMap[dateKey]) {
          dateMap[dateKey] = { count: 0, revenue: 0 };
        }
        dateMap[dateKey].count += 1;
        dateMap[dateKey].revenue += booking.total_price;
      }
    });

    return Object.entries(dateMap).map(([date, data]) => ({
      date,
      count: data.count,
      revenue: data.revenue
    }));
  };

  // Generate sport popularity data
  const generateSportPopularityData = () => {
    const sportCounts: Record<string, number> = {};
    filteredBookings.forEach(booking => {
      const sportName = booking.court?.sports?.name || 'Unknown';
      sportCounts[sportName] = (sportCounts[sportName] || 0) + 1;
    });
    return Object.entries(sportCounts).map(([name, value]) => ({ name, value }));
  };

  // Generate peak hours data
  const generateTimeSlotPopularity = () => {
    const hourCounts: Record<string, number> = {};
    filteredBookings.forEach(booking => {
      const startHour = booking.start_time.split(':')[0];
      const hourKey = `${startHour}:00`;
      hourCounts[hourKey] = (hourCounts[hourKey] || 0) + 1;
    });
    return Object.entries(hourCounts)
      .map(([hour, count]) => ({ hour, count }))
      .sort((a, b) => parseInt(a.hour) - parseInt(b.hour));
  };

  const trendData = generateBookingTrends();
  const sportPopularityData = generateSportPopularityData();
  const timeSlotData = generateTimeSlotPopularity();

  // Colors for charts
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

  const handlePreviousPeriod = () => {
    switch(timeRange) {
      case 'week':
        setCurrentDate(prev => addDays(prev, -7));
        break;
      case 'month':
        setCurrentDate(prev => subMonths(prev, 1));
        break;
      case 'year':
        setCurrentDate(prev => new Date(prev.getFullYear() - 1, prev.getMonth(), prev.getDate()));
        break;
    }
  };

  const handleNextPeriod = () => {
    const today = new Date();
    let newDate;
    switch(timeRange) {
      case 'week':
        newDate = addDays(currentDate, 7);
        break;
      case 'month':
        newDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1);
        break;
      case 'year':
        newDate = new Date(currentDate.getFullYear() + 1, currentDate.getMonth(), currentDate.getDate());
        break;
      default:
        newDate = new Date();
    }
    if (newDate <= today) {
      setCurrentDate(newDate);
    }
  };

  const getTimeRangeLabel = () => {
    switch(timeRange) {
      case 'week':
        return `${format(addDays(currentDate, -7), 'MMM dd')} - ${format(currentDate, 'MMM dd, yyyy')}`;
      case 'month':
        return format(currentDate, 'MMMM yyyy');
      case 'year':
        return format(currentDate, 'yyyy');
      default:
        return format(currentDate, 'MMMM yyyy');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4">
        <div>
          <h2 className="text-2xl font-bold text-white">Analytics Dashboard</h2>
          <p className="text-emerald-200/80">Track your venue performance and booking trends</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <PaymentMethodFilter
            selectedFilter={paymentMethodFilter}
            onFilterChange={setPaymentMethodFilter}
          />

          <select
            value={selectedVenueId}
            onChange={(e) => setSelectedVenueId(e.target.value)}
            className="px-3 py-2 border border-emerald-500/30 rounded-md bg-black/80 text-white"
          >
            {userRole === 'super_admin' && (
              <option value="all">All Venues</option>
            )}
            {venues
              .filter(venue =>
                userRole === 'super_admin' ||
                adminVenues.some(v => v.venue_id === venue.id)
              )
              .map(venue => (
                <option key={venue.id} value={venue.id}>{venue.name}</option>
              ))
            }
          </select>

          <div className="flex items-center border border-emerald-500/30 rounded-md overflow-hidden bg-black/80">
            <Button
              variant="ghost"
              onClick={handlePreviousPeriod}
              className="border-r border-emerald-500/30 text-white hover:bg-emerald-500/20"
            >
              ←
            </Button>
            <div className="px-3 flex-1 text-center">
              <span className="text-sm font-medium text-white">{getTimeRangeLabel()}</span>
            </div>
            <Button
              variant="ghost"
              onClick={handleNextPeriod}
              className="border-l border-emerald-500/30 text-white hover:bg-emerald-500/20"
              disabled={
                (timeRange === 'month' &&
                  currentDate.getMonth() === new Date().getMonth() &&
                  currentDate.getFullYear() === new Date().getFullYear()) ||
                (timeRange === 'year' &&
                  currentDate.getFullYear() === new Date().getFullYear())
              }
            >
              →
            </Button>
          </div>
        </div>

        <div className="flex rounded-md overflow-hidden border border-emerald-500/30 bg-black/80">
          <Button
            variant={timeRange === 'week' ? 'default' : 'ghost'}
            className={`rounded-none flex-1 ${timeRange === 'week' ? 'bg-emerald-500 text-white' : 'text-white hover:bg-emerald-500/20'}`}
            onClick={() => setTimeRange('week')}
          >
            Week
          </Button>
          <Button
            variant={timeRange === 'month' ? 'default' : 'ghost'}
            className={`rounded-none border-l border-r border-emerald-500/30 flex-1 ${timeRange === 'month' ? 'bg-emerald-500 text-white' : 'text-white hover:bg-emerald-500/20'}`}
            onClick={() => setTimeRange('month')}
          >
            Month
          </Button>
          <Button
            variant={timeRange === 'year' ? 'default' : 'ghost'}
            className={`rounded-none flex-1 ${timeRange === 'year' ? 'bg-emerald-500 text-white' : 'text-white hover:bg-emerald-500/20'}`}
            onClick={() => setTimeRange('year')}
          >
            Year
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5 bg-black/80 border border-emerald-500/30">
          <TabsTrigger value="overview" className="text-white data-[state=active]:bg-emerald-500 data-[state=active]:text-white">Overview</TabsTrigger>
          <TabsTrigger value="booking-trends" className="text-white data-[state=active]:bg-emerald-500 data-[state=active]:text-white">Booking Trends</TabsTrigger>
          <TabsTrigger value="popular-sports" className="text-white data-[state=active]:bg-emerald-500 data-[state=active]:text-white">Popular Sports</TabsTrigger>
          <TabsTrigger value="peak-hours" className="text-white data-[state=active]:bg-emerald-500 data-[state=active]:text-white">Peak Hours</TabsTrigger>
          <TabsTrigger value="recent-bookings" className="text-white data-[state=active]:bg-emerald-500 data-[state=active]:text-white">Recent Bookings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="bg-black/80 border-emerald-500/30">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-emerald-300">Total Bookings</CardTitle>
                <div className="flex items-baseline space-x-2">
                  <h3 className="text-3xl font-bold text-white">{filteredBookings.length}</h3>
                  {timeRange === 'month' && (
                    <p className="text-sm text-emerald-200/60">bookings this month</p>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-xs text-emerald-200/60 flex items-center">
                  <CalendarIcon className="w-4 h-4 mr-1" />
                  <span>For {getTimeRangeLabel()}</span>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-black/80 border-emerald-500/30">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-emerald-300">Total Revenue</CardTitle>
                <div className="flex flex-col gap-1">
                  <div className="flex items-baseline space-x-2">
                    <h3 className="text-2xl font-bold text-white">₹{totalRevenue.toLocaleString()}</h3>
                    <span className="text-xs text-emerald-200/60">(Gross)</span>
                  </div>
                  <div className="flex items-baseline space-x-2">
                    <h3 className="text-xl font-semibold text-emerald-400">
                      ₹{totalNetRevenue.toLocaleString()}
                    </h3>
                    <span className="text-xs text-emerald-200/60">(Net)</span>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-xs text-emerald-200/60 flex items-center">
                  <CalendarIcon className="w-4 h-4 mr-1" />
                  <span>For {getTimeRangeLabel()}</span>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-black/80 border-emerald-500/30">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-emerald-300">Average Booking Value</CardTitle>
                <div className="flex items-baseline space-x-2">
                  <h3 className="text-3xl font-bold text-white">
                    ₹{filteredBookings.length > 0
                      ? (totalRevenue / filteredBookings.length).toLocaleString(undefined, {
                          maximumFractionDigits: 2
                        })
                      : 0}
                  </h3>
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-xs text-emerald-200/60 flex items-center">
                  <UsersIcon className="w-4 h-4 mr-1" />
                  <span>Per booking</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="booking-trends" className="space-y-6">
          <Card className="bg-black/80 border-emerald-500/30">
            <CardHeader>
              <CardTitle className="text-white">Booking & Revenue Trends</CardTitle>
              <p className="text-emerald-200/80">Track daily bookings and revenue over time</p>
            </CardHeader>
            <CardContent>
              <div className="min-h-[400px] h-[450px] w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsLineChart
                    data={trendData}
                    margin={{ top: 16, right: 24, left: 8, bottom: 16 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis dataKey="date" interval="preserveStartEnd" minTickGap={8} stroke="#9CA3AF" />
                    <YAxis yAxisId="left" allowDecimals={false} stroke="#9CA3AF" />
                    <YAxis yAxisId="right" orientation="right" stroke="#9CA3AF" />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: '#1F2937',
                        border: '1px solid #374151',
                        borderRadius: '8px',
                        color: '#F9FAFB'
                      }}
                    />
                    <Legend />
                    <Line
                      yAxisId="left"
                      type="monotone"
                      dataKey="count"
                      stroke="#10B981"
                      name="Bookings"
                      strokeWidth={3}
                    />
                    <Line
                      yAxisId="right"
                      type="monotone"
                      dataKey="revenue"
                      stroke="#F59E0B"
                      name="Revenue (₹)"
                      strokeWidth={3}
                    />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="popular-sports" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="bg-black/80 border-emerald-500/30">
              <CardHeader>
                <CardTitle className="text-white">Sport Distribution</CardTitle>
                <p className="text-emerald-200/80">Breakdown of bookings by sport type</p>
              </CardHeader>
              <CardContent>
                <div className="min-h-[300px] h-[350px] w-full">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={sportPopularityData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {sportPopularityData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip
                        contentStyle={{
                          backgroundColor: '#1F2937',
                          border: '1px solid #374151',
                          borderRadius: '8px',
                          color: '#F9FAFB'
                        }}
                      />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
            <Card className="bg-black/80 border-emerald-500/30">
              <CardHeader>
                <CardTitle className="text-white">Sport Popularity</CardTitle>
                <p className="text-emerald-200/80">Number of bookings by sport type</p>
              </CardHeader>
              <CardContent>
                <div className="min-h-[300px] h-[350px] w-full">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsBarChart
                      data={sportPopularityData}
                      layout="vertical"
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                      <XAxis type="number" stroke="#9CA3AF" />
                      <YAxis dataKey="name" type="category" width={80} stroke="#9CA3AF" />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: '#1F2937',
                          border: '1px solid #374151',
                          borderRadius: '8px',
                          color: '#F9FAFB'
                        }}
                      />
                      <Bar dataKey="value" fill="#8884d8" name="Bookings">
                        {sportPopularityData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Bar>
                    </RechartsBarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="peak-hours" className="space-y-6">
          <Card className="bg-black/80 border-emerald-500/30">
            <CardHeader>
              <CardTitle className="text-white">Peak Hours Analysis</CardTitle>
              <p className="text-emerald-200/80">Busiest times and booking patterns throughout the day</p>
            </CardHeader>
            <CardContent>
              <div className="min-h-[400px] h-[450px] w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsBarChart
                    data={timeSlotData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis dataKey="hour" stroke="#9CA3AF" />
                    <YAxis stroke="#9CA3AF" />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: '#1F2937',
                        border: '1px solid #374151',
                        borderRadius: '8px',
                        color: '#F9FAFB'
                      }}
                    />
                    <Bar dataKey="count" fill="#10B981" name="Bookings" />
                  </RechartsBarChart>
                </ResponsiveContainer>
              </div>
              <div className="mt-4 text-sm text-emerald-200/60 flex items-center">
                <span>Hours shown in 24-hour format</span>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recent-bookings" className="space-y-6">
          <Card className="bg-black/80 border-emerald-500/30">
            <CardHeader>
              <CardTitle className="text-white">Recent Bookings</CardTitle>
              <p className="text-emerald-200/80">Latest booking activity and details</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {filteredBookings.slice(0, 10).map((booking) => (
                  <div key={booking.id} className="flex justify-between items-center p-3 border border-emerald-500/30 rounded-lg bg-emerald-900/20">
                    <div>
                      <p className="font-medium text-white">{booking.court?.venues?.name || 'Unknown Venue'}</p>
                      <p className="text-sm text-emerald-200/80">
                        {booking.court?.sports?.name || 'Unknown Sport'} • {format(parseISO(booking.booking_date), 'MMM dd, yyyy')}
                      </p>
                      <p className="text-xs text-emerald-200/60">
                        {booking.start_time} - {booking.end_time}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-white">₹{booking.total_price}</p>
                      <p className="text-sm text-emerald-200/80">{booking.payment_method || 'online'}</p>
                      <p className="text-xs text-emerald-200/60">{booking.status}</p>
                    </div>
                  </div>
                ))}
                {filteredBookings.length === 0 && (
                  <div className="text-center py-8 text-emerald-200/60">
                    No bookings found for the selected criteria
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AnalyticsDesktop;


import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { toast } from '@/components/ui/use-toast';
import { Loader2, MessageCircle, Search, CheckCircle, Clock, Filter, MapPin, Tag } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AdminHelpChatInterface } from '@/components/AdminHelpChatInterface';
import { HelpRequest, GetHelpRequestsResult, UpdateHelpRequestStatusResult, HELP_STATUS } from '@/types/help';

interface HelpRequestsManagementProps {
  userRole: string | null;
}

const CATEGORY_LABELS = {
  booking_issues: 'Booking Issues',
  facility_questions: 'Facility Questions', 
  payment_problems: 'Payment Problems',
  general: 'General Inquiry'
};

const HelpRequestsManagement: React.FC<HelpRequestsManagementProps> = ({ userRole }) => {
  const { user } = useAuth();
  const [helpRequests, setHelpRequests] = useState<HelpRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRequest, setSelectedRequest] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');

  useEffect(() => {
    fetchHelpRequests();

    // Set up real-time subscription for help requests
    const helpRequestsChannel = supabase
      .channel('help_requests_changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'help_requests'
      }, payload => {
        console.log('Help request change:', payload);
        fetchHelpRequests();
      })
      .subscribe();

    // Set up real-time subscription for messages
    const messagesChannel = supabase
      .channel('help_messages_channel')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'messages',
        filter: 'venue_id=is.null' // Help request messages have null venue_id
      }, payload => {
        console.log('New help message:', payload);
        fetchHelpRequests();
      })
      .subscribe();

    return () => {
      supabase.removeChannel(helpRequestsChannel);
      supabase.removeChannel(messagesChannel);
    };
  }, [userRole]);

  const fetchHelpRequests = async () => {
    try {
      setLoading(true);

      // Only super_admin can see help requests
      if (userRole !== 'super_admin') {
        setHelpRequests([]);
        setLoading(false);
        return;
      }

      // Fetch help requests using RPC call
      let { data, error } = await supabase
        .rpc('get_help_requests', {
          p_status: statusFilter === 'all' ? null : statusFilter
        })
        .returns<GetHelpRequestsResult>();

      if (error) throw error;

      // Apply filters
      const filteredRequests = (data || []).filter((req: HelpRequest) => {
        const matchesSearch = !searchQuery ||
          req.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
          req.user_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          req.user_email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          req.user_phone?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          req.venue_name?.toLowerCase().includes(searchQuery.toLowerCase());
        
        const matchesCategory = categoryFilter === 'all' || req.category === categoryFilter;
        
        return matchesSearch && matchesCategory;
      });

      setHelpRequests(filteredRequests);
    } catch (error) {
      console.error('Error fetching help requests:', error);
      toast({
        title: 'Error',
        description: 'Failed to load help requests',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = (status: string) => {
    setStatusFilter(status);
    setTimeout(() => {
      fetchHelpRequests();
    }, 100);
  };

  const handleCategoryChange = (category: string) => {
    setCategoryFilter(category);
    setTimeout(() => {
      fetchHelpRequests();
    }, 100);
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchHelpRequests();
  };

  const markAsResolved = async (requestId: string) => {
    console.log('markAsResolved called with requestId:', requestId);
    console.log('HELP_STATUS.RESOLVED:', HELP_STATUS.RESOLVED);

    try {
      console.log('Calling update_help_request_status function...');
      const { data, error } = await supabase
        .rpc('update_help_request_status', {
          p_help_request_id: requestId,
          p_status: HELP_STATUS.RESOLVED
        })
        .returns<UpdateHelpRequestStatusResult>();

      console.log('RPC response:', { data, error });

      if (error) throw error;

      toast({
        title: 'Success',
        description: 'Help request marked as resolved'
      });

      console.log('Refreshing help requests...');
      await fetchHelpRequests();
    } catch (error) {
      console.error('Error updating help request:', error);
      toast({
        title: 'Error',
        description: `Failed to update help request: ${error.message || 'Unknown error'}`,
        variant: 'destructive'
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true
    });
  };

  if (userRole !== 'super_admin') {
    return (
      <div className="text-center py-12 bg-gray-50 rounded-lg">
        <p className="text-gray-600">Only super admins can access help requests</p>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Help Requests Management</h2>
        
        <div className="flex space-x-2">
          <form onSubmit={handleSearch} className="relative w-64">
            <Input 
              placeholder="Search requests..." 
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pr-8"
            />
            <Button type="submit" variant="ghost" size="icon" className="absolute right-0 top-0 h-full">
              <Search className="h-4 w-4" />
            </Button>
          </form>
          
          <Select value={categoryFilter} onValueChange={handleCategoryChange}>
            <SelectTrigger className="w-[160px]">
              <div className="flex items-center">
                <Tag className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Category" />
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {Object.entries(CATEGORY_LABELS).map(([value, label]) => (
                <SelectItem key={value} value={value}>{label}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select value={statusFilter} onValueChange={handleStatusChange}>
            <SelectTrigger className="w-[180px]">
              <div className="flex items-center">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filter by status" />
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Requests</SelectItem>
              <SelectItem value="open">Open</SelectItem>
              <SelectItem value="in_progress">In Progress</SelectItem>
              <SelectItem value="resolved">Resolved</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 h-[70vh]">
        {/* Requests List */}
        <div className="overflow-y-auto border rounded-lg bg-emerald-800 p-4">
          <h3 className="font-bold text-lg mb-4">Help Requests</h3>
          
          {loading ? (
            <div className="flex justify-center p-8">
              <Loader2 className="animate-spin h-8 w-8 text-indigo" />
            </div>
          ) : helpRequests.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>No help requests found</p>
            </div>
          ) : (
            <div className="space-y-2">
              {helpRequests.map(request => (
                <div 
                  key={request.id}
                  onClick={() => setSelectedRequest(request.id)}
                  className={`p-3 rounded-lg cursor-pointer ${
                    selectedRequest === request.id 
                      ? 'bg-indigo text-white' 
                      : 'hover:bg-gray-100'
                  }`}
                >
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className={`text-xs font-mono ${
                          selectedRequest === request.id ? 'text-white/80' : 'text-gray-500'
                        }`}>
                          #{request.ticket_number}
                        </span>
                        <Badge className={`text-xs ${
                          request.status === HELP_STATUS.RESOLVED
                            ? 'bg-green-100 text-green-800'
                            : request.status === HELP_STATUS.IN_PROGRESS
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-orange-100 text-orange-800'
                        }`}>
                          {request.status === HELP_STATUS.OPEN ? 'OPEN' :
                           request.status === HELP_STATUS.IN_PROGRESS ? 'IN PROGRESS' :
                           'RESOLVED'}
                        </Badge>
                      </div>
                      <p className={`font-medium ${
                        selectedRequest === request.id ? 'text-white' : 'text-slate-800'
                      }`}>
                        {request.subject}
                      </p>
                    </div>
                  </div>
                  
                  <div className="space-y-1">
                    <p className={`text-sm ${
                      selectedRequest === request.id ? 'text-white/80' : 'text-gray-600'
                    }`}>
                      {request.user_name} ({request.user_email}
                      {request.user_phone && ` • ${request.user_phone}`})
                    </p>
                    
                    {request.venue_name && (
                      <div className={`flex items-center text-xs ${
                        selectedRequest === request.id ? 'text-white/70' : 'text-gray-500'
                      }`}>
                        <MapPin className="h-3 w-3 mr-1" />
                        <span>{request.venue_name}</span>
                      </div>
                    )}
                    
                    <div className={`flex items-center text-xs ${
                      selectedRequest === request.id ? 'text-white/70' : 'text-gray-500'
                    }`}>
                      <Tag className="h-3 w-3 mr-1" />
                      <span>{CATEGORY_LABELS[request.category as keyof typeof CATEGORY_LABELS] || request.category}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center mt-2 text-xs">
                    {request.status === HELP_STATUS.RESOLVED ? (
                      <CheckCircle className={`h-3 w-3 mr-1 ${
                        selectedRequest === request.id ? 'text-white/70' : 'text-green-500'
                      }`} />
                    ) : (
                      <Clock className={`h-3 w-3 mr-1 ${
                        selectedRequest === request.id ? 'text-white/70' : 'text-orange-500'
                      }`} />
                    )}
                    <span className={selectedRequest === request.id ? 'text-white/70' : 'text-gray-500'}>
                      Last activity: {formatDate(request.last_message_at)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
        
        {/* Chat Interface */}
        <div className="col-span-2 border rounded-lg bg-white flex flex-col overflow-hidden">
          {selectedRequest ? (
            <AdminHelpChatInterface 
              selectedRequestId={selectedRequest}
              onMarkResolved={() => markAsResolved(selectedRequest)}
              helpRequests={helpRequests}
            />
          ) : (
            <div className="h-full flex items-center justify-center text-gray-500">
              <div className="text-center">
                <MessageCircle className="h-12 w-12 text-gray-300 mx-auto mb-2" />
                <p>Select a help request to view the conversation</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default HelpRequestsManagement;

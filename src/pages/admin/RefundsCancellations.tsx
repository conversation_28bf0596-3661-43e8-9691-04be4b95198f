import React, { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  RefreshCw, 
  Download, 
  Search, 
  Filter,
  DollarSign,
  AlertCircle,
  CheckCircle,
  XCircle,
  Clock,
  Calendar,
  MapPin,
  User,
  Edit3
} from 'lucide-react';
import { format, parseISO } from 'date-fns';
import { CancellationWithDetails, CancellationFilters, CancellationStats } from '@/types/cancellation';
import RefundStatusModal from '@/components/admin/RefundStatusModal';

const RefundsCancellations_Mobile: React.FC = () => {
  const { user, userRole } = useAuth();
  const [cancellations, setCancellations] = useState<CancellationWithDetails[]>([]);
  const [stats, setStats] = useState<CancellationStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<CancellationFilters>({
    refund_status: 'all',
    cancelled_by_role: 'all',
    search_query: ''
  });
  const [showRefundModal, setShowRefundModal] = useState(false);
  const [selectedCancellation, setSelectedCancellation] = useState<CancellationWithDetails | null>(null);
  const [showFilters, setShowFilters] = useState(false);

  // Only super_admin can access this page
  if (userRole !== 'super_admin') {
    return (
      <div className="flex items-center justify-center h-64 p-4">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600">Only super administrators can access refunds and cancellations.</p>
        </div>
      </div>
    );
  }

  useEffect(() => {
    fetchCancellations();
  }, [filters]);

  const fetchCancellations = async () => {
    try {
      setLoading(true);
      
      if (!user?.id) {
        throw new Error('User not authenticated');
      }

      // Call the database function to get cancellations with details
      const { data, error } = await supabase.rpc('get_cancellations_with_details', {
        admin_user_id: user.id,
        limit_count: 100,
        offset_count: 0
      });

      if (error) throw error;

      let filteredData = data || [];

      // Apply client-side filters
      if (filters.refund_status && filters.refund_status !== 'all') {
        filteredData = filteredData.filter(c => c.refund_status === filters.refund_status);
      }

      if (filters.cancelled_by_role && filters.cancelled_by_role !== 'all') {
        filteredData = filteredData.filter(c => c.cancelled_by_role === filters.cancelled_by_role);
      }

      if (filters.search_query) {
        const query = filters.search_query.toLowerCase();
        filteredData = filteredData.filter(c => 
          c.customer_name.toLowerCase().includes(query) ||
          c.venue_name.toLowerCase().includes(query) ||
          c.court_name.toLowerCase().includes(query) ||
          c.cancellation_reason.toLowerCase().includes(query)
        );
      }

      setCancellations(filteredData);
      calculateStats(filteredData);
    } catch (error) {
      console.error('Error fetching cancellations:', error);
      toast({
        title: 'Error',
        description: 'Failed to load cancellations data',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (data: CancellationWithDetails[]) => {
    const totalCancellations = data.length;
    const pendingRefunds = data.filter(c => c.refund_status === 'pending').length;
    const processedRefunds = data.filter(c => c.refund_status === 'processed').length;
    const totalRefundAmount = data.reduce((sum, c) => sum + (c.refund_amount || 0), 0);
    const avgRefundAmount = totalCancellations > 0 ? totalRefundAmount / totalCancellations : 0;
    
    const cancellationsByRole = {
      admin: data.filter(c => c.cancelled_by_role === 'admin').length,
      super_admin: data.filter(c => c.cancelled_by_role === 'super_admin').length
    };

    setStats({
      total_cancellations: totalCancellations,
      pending_refunds: pendingRefunds,
      processed_refunds: processedRefunds,
      total_refund_amount: totalRefundAmount,
      avg_refund_amount: avgRefundAmount,
      cancellations_by_role: cancellationsByRole
    });
  };

  const handleRefundStatusUpdate = async (cancellationId: string, newStatus: string, notes?: string, razorpayRefundId?: string, adminRefundAmount?: number) => {
    try {
      const { data, error } = await supabase.rpc('update_refund_status', {
        cancellation_id: cancellationId,
        new_status: newStatus,
        refund_notes: notes,
        razorpay_refund_id: razorpayRefundId,
        admin_refund_amount: adminRefundAmount
      });

      if (error) throw error;

      toast({
        title: 'Refund Status Updated',
        description: `Refund status has been updated to ${newStatus}${adminRefundAmount ? ` with custom amount ₹${adminRefundAmount.toLocaleString()}` : ''}`,
      });

      // Refresh data
      fetchCancellations();
      setShowRefundModal(false);
      setSelectedCancellation(null);
    } catch (error) {
      console.error('Error updating refund status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update refund status',
        variant: 'destructive',
      });
    }
  };

  const getRefundStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 text-xs">Pending</Badge>;
      case 'processed':
        return <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs">Processed</Badge>;
      case 'rejected':
        return <Badge variant="secondary" className="bg-red-100 text-red-800 text-xs">Rejected</Badge>;
      case 'not_applicable':
        return <Badge variant="secondary" className="bg-gray-100 text-gray-800 text-xs">N/A</Badge>;
      default:
        return <Badge variant="secondary" className="text-xs">{status}</Badge>;
    }
  };

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'super_admin':
        return <Badge variant="secondary" className="bg-purple-100 text-purple-800 text-xs">Super Admin</Badge>;
      case 'admin':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800 text-xs">Admin</Badge>;
      default:
        return <Badge variant="secondary" className="text-xs">{role}</Badge>;
    }
  };

  const formatTime = (timeString: string) => {
    return format(parseISO(`2000-01-01T${timeString}`), 'h:mm a');
  };

  const formatDate = (dateString: string) => {
    return format(parseISO(dateString), 'MMM dd, yyyy');
  };

  const formatDateTime = (dateTimeString: string) => {
    return format(parseISO(dateTimeString), 'MMM dd h:mm a');
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-40">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-sport-green"></div>
      </div>
    );
  }

  return (
    <div className="p-4 space-y-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-xl font-bold text-gray-900">Refunds & Cancellations</h1>
          <p className="text-sm text-gray-600">Manage cancellations and refunds</p>
        </div>
        <Button onClick={fetchCancellations} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4" />
        </Button>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-2 gap-3">
          <Card>
            <CardContent className="p-3">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs text-gray-600">Total</p>
                  <p className="text-lg font-bold">{stats.total_cancellations}</p>
                </div>
                <AlertCircle className="h-6 w-6 text-orange-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-3">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs text-gray-600">Pending</p>
                  <p className="text-lg font-bold text-yellow-600">{stats.pending_refunds}</p>
                </div>
                <Clock className="h-6 w-6 text-yellow-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-3">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs text-gray-600">Processed</p>
                  <p className="text-lg font-bold text-green-600">{stats.processed_refunds}</p>
                </div>
                <CheckCircle className="h-6 w-6 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-3">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs text-gray-600">Total Amount</p>
                  <p className="text-sm font-bold">₹{stats.total_refund_amount.toLocaleString()}</p>
                </div>
                <DollarSign className="h-6 w-6 text-blue-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters Toggle */}
      <Button
        onClick={() => setShowFilters(!showFilters)}
        variant="outline"
        className="w-full"
      >
        <Filter className="h-4 w-4 mr-2" />
        {showFilters ? 'Hide Filters' : 'Show Filters'}
      </Button>

      {/* Filters */}
      {showFilters && (
        <Card>
          <CardContent className="p-4 space-y-3">
            <div>
              <label className="block text-sm font-medium mb-1">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Customer, venue..."
                  value={filters.search_query || ''}
                  onChange={(e) => setFilters(prev => ({ ...prev, search_query: e.target.value }))}
                  className="pl-10"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Refund Status</label>
              <Select
                value={filters.refund_status || 'all'}
                onValueChange={(value) => setFilters(prev => ({ ...prev, refund_status: value as any }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="processed">Processed</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="not_applicable">Not Applicable</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Cancelled By</label>
              <Select
                value={filters.cancelled_by_role || 'all'}
                onValueChange={(value) => setFilters(prev => ({ ...prev, cancelled_by_role: value as any }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Roles</SelectItem>
                  <SelectItem value="admin">Venue Admin</SelectItem>
                  <SelectItem value="super_admin">Super Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button 
              onClick={() => setFilters({ refund_status: 'all', cancelled_by_role: 'all', search_query: '' })}
              variant="outline"
              className="w-full"
            >
              Clear Filters
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Cancellations List */}
      <div className="space-y-3">
        {cancellations.length === 0 ? (
          <Card>
            <CardContent className="p-6 text-center">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Cancellations Found</h3>
              <p className="text-gray-600">There are no cancellations matching your criteria.</p>
            </CardContent>
          </Card>
        ) : (
          cancellations.map((cancellation) => (
            <Card key={cancellation.cancellation_id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="space-y-3">
                  {/* Header */}
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <User className="h-4 w-4 text-gray-400" />
                        <span className="font-medium text-sm">{cancellation.customer_name}</span>
                      </div>
                      <div className="flex items-center gap-2 text-xs text-white">
                        <MapPin className="h-3 w-3 text-white" />
                        <span className="text-white">{cancellation.venue_name} • {cancellation.court_name}</span>
                      </div>
                    </div>
                    <div className="flex flex-col gap-1 items-end">
                      {getRefundStatusBadge(cancellation.refund_status)}
                      {getRoleBadge(cancellation.cancelled_by_role)}
                    </div>
                  </div>

                  {/* Booking Details */}
                  <div className="bg-gray-50 p-3 rounded text-xs space-y-1">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-3 w-3 text-gray-400" />
                      <span className="text-black">{formatDate(cancellation.booking_date)} • {formatTime(cancellation.start_time)} - {formatTime(cancellation.end_time)}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-3 w-3 text-gray-400" />
                      <span className="text-black">₹{cancellation.total_price.toLocaleString()} • Refund: ₹{(cancellation.refund_amount || 0).toLocaleString()}</span>
                    </div>
                  </div>

                  {/* Cancellation Reason */}
                  <div className="text-xs">
                    <span className="text-gray-500">Reason:</span>
                    <div className="bg-gray-50 p-2 rounded mt-1 text-gray-700">
                      {cancellation.cancellation_reason}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex justify-between items-center pt-2">
                    <span className="text-xs text-gray-500">
                      Cancelled {formatDateTime(cancellation.cancelled_at)}
                    </span>
                    <Button
                      onClick={() => {
                        setSelectedCancellation(cancellation);
                        setShowRefundModal(true);
                      }}
                      variant="outline"
                      size="sm"
                    >
                      <Edit3 className="h-3 w-3 mr-1" />
                      Update
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Refund Status Modal */}
      <RefundStatusModal
        isOpen={showRefundModal}
        onClose={() => {
          setShowRefundModal(false);
          setSelectedCancellation(null);
        }}
        cancellation={selectedCancellation}
        onUpdate={handleRefundStatusUpdate}
      />
    </div>
  );
};

export default RefundsCancellations_Mobile;

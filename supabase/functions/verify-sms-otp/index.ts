import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client with service role
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Parse request body
    const { phone, otp } = await req.json()

    if (!phone || !otp) {
      return new Response(
        JSON.stringify({ error: 'Phone number and OTP are required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Clean and format phone number
    const cleanPhone = phone.replace(/[\s()-]/g, '')
    const formattedPhone = cleanPhone.startsWith('+91') ? cleanPhone : 
                          cleanPhone.startsWith('91') ? '+' + cleanPhone : 
                          '+91' + cleanPhone

    // Validate OTP format
    if (!/^\d{6}$/.test(otp)) {
      return new Response(
        JSON.stringify({ 
          success: false,
          error: 'Invalid OTP format. Must be 6 digits.' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    console.log('Verifying SMS OTP for phone:', formattedPhone)

    // Validate OTP using optimized database function
    const { data: validation, error: validationError } = await supabaseAdmin
      .rpc('validate_sms_otp', {
        phone_number: formattedPhone,
        otp_input: otp,
        purpose_input: 'registration'
      })

    if (validationError) {
      console.error('SMS OTP validation error:', validationError)
      return new Response(
        JSON.stringify({
          success: false,
          error: 'OTP validation failed'
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    if (!validation.is_valid) {
      console.log('Invalid SMS OTP for phone:', formattedPhone, 'Error:', validation.error_message)
      return new Response(
        JSON.stringify({
          success: false,
          error: validation.error_message || 'Invalid or expired OTP'
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // OTP is valid, create Supabase user
    const userData = validation.user_data
    console.log('Creating Supabase user for verified phone:', formattedPhone)

    // Create user in Supabase Auth with temporary email (same as WhatsApp flow)
    const tempEmail = `${crypto.randomUUID()}@temp.grid2play.com`

    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email: tempEmail,
      phone: formattedPhone,
      password: userData.password_hash,
      email_confirm: true, // Confirm temp email to avoid verification issues
      phone_confirm: true, // Mark phone as confirmed
      user_metadata: {
        full_name: userData.full_name,
        phone: formattedPhone,
        registration_method: 'sms', // Changed from 'whatsapp' to 'sms'
        temp_email: true
      }
    })

    if (authError) {
      console.error('Error creating Supabase user:', authError)
      return new Response(
        JSON.stringify({ 
          success: false,
          error: 'Failed to create user account' 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    console.log('Supabase user created successfully:', authData.user.id)

    // Update user profile with phone verification
    const { error: profileError } = await supabaseAdmin
      .from('profiles')
      .update({
        phone: formattedPhone,
        phone_verified: true,
        full_name: userData.full_name,
        email: tempEmail,
        updated_at: new Date().toISOString()
      })
      .eq('id', authData.user.id)

    if (profileError) {
      console.error('Error updating profile:', profileError)
      // Don't fail the request, user is created but profile update failed
    }

    // Clean up pending SMS OTP record
    const { error: cleanupError } = await supabaseAdmin
      .from('pending_whatsapp_users')
      .delete()
      .eq('phone', formattedPhone)
      .eq('purpose', 'registration')

    if (cleanupError) {
      console.error('Error cleaning up pending SMS OTP:', cleanupError)
      // Don't fail the request, cleanup is not critical
    }

    // Create session for the user
    const { data: sessionData, error: sessionError } = await supabaseAdmin.auth.admin.generateLink({
      type: 'magiclink',
      email: tempEmail,
      options: {
        redirectTo: `${Deno.env.get('SUPABASE_URL')}/auth/v1/callback`
      }
    })

    if (sessionError) {
      console.error('Error generating session:', sessionError)
      return new Response(
        JSON.stringify({ 
          success: false,
          error: 'Failed to create user session' 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    return new Response(
      JSON.stringify({ 
        success: true,
        message: 'SMS OTP verified successfully',
        user: {
          id: authData.user.id,
          phone: formattedPhone,
          full_name: userData.full_name,
          email: tempEmail,
          phone_verified: true
        },
        sessionUrl: sessionData.properties?.action_link
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  } catch (error) {
    console.error('Error in verify-sms-otp function:', error)
    return new Response(
      JSON.stringify({ 
        success: false,
        error: 'Internal server error',
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

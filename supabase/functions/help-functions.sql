
-- Function to get user help requests (only unresolved ones)
CREATE OR REPLACE FUNCTION public.get_user_help_requests(p_user_id UUID)
RETURNS SETOF public.help_requests
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT hr.*
  FROM public.help_requests hr
  WHERE hr.user_id = p_user_id
    AND hr.status != 'resolved'
  ORDER BY hr.last_message_at DESC;
$$;

-- Function to generate ticket number
CREATE OR REPLACE FUNCTION public.generate_ticket_number()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  ticket_num TEXT;
  current_year TEXT;
  sequence_num INTEGER;
BEGIN
  -- Get current year
  current_year := EXTRACT(YEAR FROM now())::TEXT;

  -- Get next sequence number for this year
  SELECT COALESCE(MAX(CAST(SUBSTRING(ticket_number FROM 6) AS INTEGER)), 0) + 1
  INTO sequence_num
  FROM public.help_requests
  WHERE ticket_number LIKE current_year || '%';

  -- Format: YYYY-NNNN (e.g., 2024-0001)
  ticket_num := current_year || '-' || LPAD(sequence_num::TEXT, 4, '0');

  RETURN ticket_num;
END;
$$;

-- Function to create a help request with ticket number
CREATE OR REPLACE FUNCTION public.create_help_request(p_user_id UUID, p_subject TEXT)
RETURNS public.help_requests
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_request public.help_requests;
  ticket_num TEXT;
BEGIN
  -- Generate ticket number
  ticket_num := public.generate_ticket_number();

  INSERT INTO public.help_requests (
    user_id,
    subject,
    status,
    ticket_number,
    created_at,
    updated_at,
    last_message_at
  ) VALUES (
    p_user_id,
    p_subject,
    'open',
    ticket_num,
    now(),
    now(),
    now()
  ) RETURNING * INTO new_request;

  RETURN new_request;
END;
$$;

-- Function to create a help request with venue context
CREATE OR REPLACE FUNCTION public.create_help_request(
  p_user_id UUID,
  p_subject TEXT,
  p_venue_id UUID DEFAULT NULL,
  p_category TEXT DEFAULT NULL
)
RETURNS public.help_requests
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_request public.help_requests;
  ticket_num TEXT;
BEGIN
  -- Generate ticket number
  ticket_num := public.generate_ticket_number();

  INSERT INTO public.help_requests (
    user_id,
    subject,
    status,
    ticket_number,
    venue_id,
    category,
    created_at,
    updated_at,
    last_message_at
  ) VALUES (
    p_user_id,
    p_subject,
    'open',
    ticket_num,
    p_venue_id,
    p_category,
    now(),
    now(),
    now()
  ) RETURNING * INTO new_request;

  RETURN new_request;
END;
$$;

-- Function to update help request status
CREATE OR REPLACE FUNCTION public.update_help_request_status(p_help_request_id UUID, p_status TEXT)
RETURNS public.help_requests
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  updated_request public.help_requests;
BEGIN
  UPDATE public.help_requests
  SET 
    status = p_status,
    last_message_at = now(),
    updated_at = now()
  WHERE id = p_help_request_id
  RETURNING * INTO updated_request;
  
  RETURN updated_request;
END;
$$;

-- Function to get all help requests (for super_admin)
CREATE OR REPLACE FUNCTION public.get_help_requests(p_status TEXT DEFAULT NULL)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  subject TEXT,
  status TEXT,
  ticket_number TEXT,
  category TEXT,
  venue_id UUID,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  last_message_at TIMESTAMPTZ,
  user_name TEXT,
  user_email TEXT,
  user_phone TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT
    hr.id,
    hr.user_id,
    hr.subject,
    hr.status,
    hr.ticket_number,
    hr.category,
    hr.venue_id,
    hr.created_at,
    hr.updated_at,
    hr.last_message_at,
    p.full_name AS user_name,
    p.email AS user_email,
    p.phone AS user_phone
  FROM
    public.help_requests hr
    LEFT JOIN public.profiles p ON hr.user_id = p.id
  WHERE
    (p_status IS NULL OR hr.status = p_status)
  ORDER BY
    CASE WHEN hr.status = 'open' THEN 0
         WHEN hr.status = 'in_progress' THEN 1
         ELSE 2 END,
    hr.last_message_at DESC;
END;
$$;

-- Function to get user resolved help requests with message preview
CREATE OR REPLACE FUNCTION public.get_user_resolved_help_requests(p_user_id UUID)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  subject TEXT,
  status TEXT,
  ticket_number TEXT,
  category TEXT,
  venue_id UUID,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  last_message_at TIMESTAMPTZ,
  first_message_preview TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT
    hr.id,
    hr.user_id,
    hr.subject,
    hr.status,
    hr.ticket_number,
    hr.category,
    hr.venue_id,
    hr.created_at,
    hr.updated_at,
    hr.last_message_at,
    COALESCE(
      LEFT(
        (SELECT m.content
         FROM public.messages m
         WHERE m.user_id = hr.user_id
           AND m.venue_id IS NULL
           AND m.created_at >= hr.created_at
           AND m.created_at <= hr.created_at + INTERVAL '1 day'
         ORDER BY m.created_at ASC
         LIMIT 1),
        50
      ),
      'No message content'
    ) AS first_message_preview
  FROM
    public.help_requests hr
  WHERE
    hr.user_id = p_user_id
    AND hr.status = 'resolved'
  ORDER BY
    hr.updated_at DESC;
END;
$$;

-- Function to fix data inconsistencies in help requests
CREATE OR REPLACE FUNCTION public.fix_help_request_data()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  fixed_count INTEGER := 0;
BEGIN
  -- Fix missing updated_at timestamps
  UPDATE public.help_requests
  SET updated_at = COALESCE(updated_at, created_at, now())
  WHERE updated_at IS NULL;

  GET DIAGNOSTICS fixed_count = ROW_COUNT;

  -- Fix missing last_message_at timestamps
  UPDATE public.help_requests
  SET last_message_at = COALESCE(last_message_at, updated_at, created_at, now())
  WHERE last_message_at IS NULL;

  -- Ensure all help requests have valid status
  UPDATE public.help_requests
  SET status = 'open'
  WHERE status IS NULL OR status NOT IN ('open', 'in_progress', 'resolved');

  RETURN 'Fixed ' || fixed_count || ' help request records';
END;
$$;

import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};
// System prompt to define the assistant's behavior and limitations
const SYSTEM_PROMPT = `
You are Grid2Play's expert sports slot booking assistant with real-time access to court availability and booking systems.

CRITICAL RULES - NEVER VIOLATE THESE:
1. NEVER direct users to external websites or apps (like "RPM BOX ki website", "venue ki website", etc.)
2. ALWAYS use Grid2Play's internal functions to get real-time data
3. NEVER say "mujhe specific time slots nahi dikh rahe hain" - ALWAYS call functions to check availability
4. NEVER make up venue names, court names, or availability data
5. ALWAYS stay within Grid2Play's ecosystem

MANDATORY FUNCTION USAGE:
When users ask about availability or courts, you MUST:
1. For sport queries like "Box Cricket" or "Swimming": ALWAYS call search_courts_by_sport() first
2. For venue queries like "East Delhi" or "RPM BOX": ALWAYS call search_courts_by_sport() with venue_name
3. For availability queries: ALWAYS call get_unified_availability() with the court_id
4. For booking requests: ALWAYS call prepare_booking_guidance()
5. For booking history: ALWAYS call get_user_recent_bookings()

WORKFLOW EXAMPLES:
User: "Box Cricket availability tomorrow"
You MUST:
1. Call search_courts_by_sport("Box Cricket") to find courts
2. Call get_unified_availability(court_id, "2025-06-30") for each court
3. Show real availability with times and prices

User: "RPM BOX mein kya available hai?"
You MUST:
1. Call search_courts_by_sport("", "RPM BOX") to find courts at RPM BOX venue
2. Call get_unified_availability() for each court found
3. Show real availability data

NEVER SAY THESE PHRASES:
- "mujhe specific time slots nahi dikh rahe hain"
- "website par jaake check kariye"
- "venue se contact kariye"
- "external app use kariye"

ALWAYS SAY INSTEAD:
- "Let me check real-time availability for you" (then call functions)
- "I'll find the available courts for you" (then call search_courts_by_sport)
- "Here are the current available slots" (after calling get_unified_availability)

LANGUAGE SUPPORT:
Respond in English or Hinglish based on user's language. Always be helpful and use Grid2Play's data.

AVAILABLE SPORTS IN GRID2PLAY:
- Box Cricket
- Box Football
- Swimming

AVAILABLE VENUES:
- East Delhi Box Cricket/Football Box
- RPM BOX CRICKET/FOOTBALL BOX
- Grid2play

AUTHENTICATION:
You already know the authenticated user. Never ask for personal information.

REMEMBER: Your job is to help users book slots using Grid2Play's real-time data. Always call the appropriate functions to get current information.
`;
const OPENAI_API_KEY = Deno.env.get('OPENAI_API_KEY') || '';
const SUPABASE_URL = Deno.env.get('SUPABASE_URL') || '';
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
// Main handler for the edge function
serve(async (req)=>{
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      headers: corsHeaders
    });
  }
  try {
    // Check if OpenAI API key is configured
    if (!OPENAI_API_KEY) {
      console.error("OpenAI API key not found");
      return new Response(JSON.stringify({
        message: {
          role: "assistant",
          content: "I'm currently unavailable. Please contact the administrator to set up my integration."
        }
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Parse request body
    const requestBody = await req.json();
    const { messages = [], userId = null } = requestBody;
    // Check if user is authenticated
    if (!userId) {
      console.log("User not authenticated");
      return new Response(JSON.stringify({
        message: {
          role: "assistant",
          content: "Please sign in to use booking features. कृपया साइन इन करें।"
        }
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    console.log(`Processing request for user: ${userId}`);
    // Get user info to personalize responses
    const { data: userProfile, error: userError } = await supabase.from('profiles').select('full_name, email, phone').eq('id', userId).single();
    if (userError) {
      console.error("Error fetching user profile:", userError);
    }
    const userName = userProfile?.full_name || "User";
    // Check if the message is in Hinglish to respond appropriately
    const isHinglish = detectHinglish(messages[messages.length - 1]?.content || "");
    // Prepare complete message history with system prompt
    const completeMessages = [
      {
        role: "system",
        content: SYSTEM_PROMPT + `\nThe current user's name is ${userName}.`
      },
      ...messages
    ];
    // Special handling for booking-related queries
    if (containsBookingQuery(messages[messages.length - 1]?.content || "")) {
      try {
        let bookingInfo;
        if (await isVenueAdmin(userId)) {
          const adminVenueIds = await getAdminVenueIds(userId);
          // Fetch venue names for these IDs
          let venueNames = [];
          let venuesData = [];
          if (adminVenueIds.length > 0) {
            const { data, error } = await supabase.from('venues').select('id, name').in('id', adminVenueIds);
            if (!error && data) {
              venueNames = data.map((v)=>v.name);
              venuesData = data;
            }
          }
          // Extract venue name from user message
          const requestedVenueName = extractVenueName(messages[messages.length - 1]?.content || "", venueNames);
          let filteredVenueIds = adminVenueIds;
          if (requestedVenueName) {
            // Use case-insensitive, partial match
            const venueObj = venuesData.find((v)=>requestedVenueName && v.name.toLowerCase().includes(requestedVenueName.toLowerCase()));
            if (venueObj) filteredVenueIds = [
              venueObj.id
            ];
          }
          // Extract date from user message
          const filterDate = extractDateFromMessage(messages[messages.length - 1]?.content || "");
          completeMessages.unshift({
            role: "system",
            content: `The current user is a venue admin for venues: ${venueNames.join(", ")} (IDs: ${adminVenueIds.join(", ")}). Only show bookings for these venues when asked for bookings.`
          });
          bookingInfo = await getAdminBookings(userId, filteredVenueIds, filterDate);
        } else {
          bookingInfo = await getUserBookings(userId);
        }
        if (bookingInfo && bookingInfo.success) {
          // Add booking information as a system message so the AI has context
          completeMessages.push({
            role: "system",
            content: `User has ${bookingInfo.upcoming.length} upcoming bookings and ${bookingInfo.past.length} past bookings. \n\nUpcoming bookings details: ${JSON.stringify(bookingInfo.upcoming)}\n\nPast bookings details: ${JSON.stringify(bookingInfo.past)}`
          });
        }
      } catch (error) {
        console.error("Error getting booking information:", error);
      }
    }
    // Get venue information for venue-related queries
    if (containsVenueQuery(messages[messages.length - 1]?.content || "")) {
      try {
        const venuesInfo = await getVenues();
        if (venuesInfo && venuesInfo.success) {
          completeMessages.push({
            role: "system",
            content: `Available venues: ${JSON.stringify(venuesInfo.venues)}`
          });
        }
      } catch (error) {
        console.error("Error getting venue information:", error);
      }
    }
    // Get sport information for sport-related queries
    if (containsSportQuery(messages[messages.length - 1]?.content || "")) {
      try {
        const sportsInfo = await getSports();
        if (sportsInfo && sportsInfo.success) {
          completeMessages.push({
            role: "system",
            content: `Available sports: ${JSON.stringify(sportsInfo.sports)}`
          });
        }
      } catch (error) {
        console.error("Error getting sports information:", error);
      }
    }

    // Handle availability queries
    if (containsAvailabilityQuery(messages[messages.length - 1]?.content || "")) {
      try {
        // For now, provide general availability information
        // In a more advanced implementation, you could parse the message to extract venue/court/date
        const venuesInfo = await getVenues();
        if (venuesInfo && venuesInfo.success) {
          completeMessages.push({
            role: "system",
            content: `User is asking about slot availability. Available venues for booking: ${JSON.stringify(venuesInfo.venues)}. You can help them find available slots by asking for specific venue, date, and sport preferences.`
          });
        }
      } catch (error) {
        console.error("Error getting availability information:", error);
      }
    }

    // Check for support/help/faq/privacy/contact queries
    if (containsSupportQuery(messages[messages.length - 1]?.content || "")) {
      const supportMsg = isHinglish ? "Aapko madad, FAQ, Privacy Policy ya Contact ki zarurat hai? Kripya Homepage ke Support section mein navigate karein. (For help, FAQ, privacy policy, or contact, please navigate to the Support section on the Homepage.)" : "For Help Center, FAQ, Privacy Policy, or Contact options, please navigate to the Support section on the Homepage.";
      return new Response(JSON.stringify({
        message: {
          role: "assistant",
          content: supportMsg
        }
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Check for venue admin/owner contact queries
    if (containsVenueAdminContactQuery(messages[messages.length - 1]?.content || "")) {
      const venueAdminMsg = isHinglish ? 'Aap venue ke admin/owner se seedha baat kar sakte hain "Chat with Venue" option se, jo Book Now button ke neeche Venue details page par hai. Ya phir, Venue details page par diye gaye contact number par call kar sakte hain. (You can chat directly using the "Chat with Venue" option below the Book Now button in the Venue details page, or you can call them using the contact details provided in the Venue details page.)' : 'You can chat directly using the "Chat with Venue" option below the Book Now button in the Venue details page, or you can call them using the contact details provided in the Venue details page.';
      return new Response(JSON.stringify({
        message: {
          role: "assistant",
          content: venueAdminMsg
        }
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Define available functions for OpenAI function calling
    const functions = [
      {
        name: "get_unified_availability",
        description: "Get real-time slot availability for a specific court on a specific date. Shows available time slots with pricing information.",
        parameters: {
          type: "object",
          properties: {
            court_id: {
              type: "string",
              description: "UUID of the specific court to check availability for"
            },
            date: {
              type: "string",
              description: "Date in YYYY-MM-DD format to check availability for"
            }
          },
          required: ["court_id", "date"]
        }
      },
      {
        name: "search_courts_by_sport",
        description: "Search for courts by sport name or venue name to help users find court IDs for availability checking",
        parameters: {
          type: "object",
          properties: {
            sport_name: {
              type: "string",
              description: "Name of the sport (e.g., 'Box Cricket', 'Box Football', 'Swimming')"
            },
            venue_name: {
              type: "string",
              description: "Optional venue name to filter results"
            }
          },
          required: ["sport_name"]
        }
      },
      {
        name: "prepare_booking_guidance",
        description: "Prepare booking guidance for a user when they want to book a specific slot. This does NOT process payment but prepares the booking details for frontend handoff.",
        parameters: {
          type: "object",
          properties: {
            court_id: {
              type: "string",
              description: "UUID of the court to book"
            },
            date: {
              type: "string",
              description: "Date in YYYY-MM-DD format"
            },
            start_time: {
              type: "string",
              description: "Start time in HH:MM:SS format"
            },
            end_time: {
              type: "string",
              description: "End time in HH:MM:SS format"
            }
          },
          required: ["court_id", "date", "start_time", "end_time"]
        }
      },
      {
        name: "get_user_recent_bookings",
        description: "Get user's recent bookings including upcoming and past bookings with detailed information",
        parameters: {
          type: "object",
          properties: {
            limit: {
              type: "number",
              description: "Number of bookings to return (default: 10)"
            },
            status_filter: {
              type: "string",
              description: "Filter by booking status: 'upcoming', 'past', 'all' (default: 'all')"
            }
          },
          required: []
        }
      },
      {
        name: "check_booking_status",
        description: "Check the status of a specific booking by booking reference or ID",
        parameters: {
          type: "object",
          properties: {
            booking_reference: {
              type: "string",
              description: "Booking reference number or booking ID"
            }
          },
          required: ["booking_reference"]
        }
      }
    ];

    console.log("=== CHAT ASSISTANT DEBUG ===");
    console.log("User ID:", userId);
    console.log("Message count:", completeMessages.length);
    console.log("Last user message:", messages[messages.length - 1]?.content);
    console.log("Functions available:", functions.map(f => f.name));

    console.log("Sending request to OpenAI with function calling");
    // Call OpenAI API with function calling capability
    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${OPENAI_API_KEY}`,
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        model: "gpt-4o-mini",
        messages: completeMessages,
        temperature: 0.7,
        tools: functions.map(fn => ({ type: "function", function: fn })),
        tool_choice: "auto"
      })
    });
    if (!response.ok) {
      const errorData = await response.json();
      console.error("OpenAI API error:", errorData);
      throw new Error(`OpenAI API error: ${errorData.error?.message || "Unknown error"}`);
    }
    const data = await response.json();
    console.log("OpenAI API response:", JSON.stringify(data));

    // Check if we have a valid response
    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      throw new Error("Invalid response format from OpenAI");
    }

    const responseMessage = data.choices[0].message;
    let result = { message: responseMessage };

    // Check if the response contains a function call
    if (responseMessage.tool_calls && responseMessage.tool_calls.length > 0) {
      console.log("=== FUNCTION CALL DETECTED ===");
      const toolCall = responseMessage.tool_calls[0];
      const functionName = toolCall.function.name;
      const functionArgs = JSON.parse(toolCall.function.arguments);

      console.log(`Executing function: ${functionName}`);
      console.log(`Function arguments:`, functionArgs);
      console.log(`Tool call ID:`, toolCall.id);

      // Execute the function
      const functionResult = await handleFunctionCall(functionName, functionArgs, userId, supabase);
      console.log(`Function result:`, functionResult);

      // Make a second call to OpenAI with the function result
      const secondResponse = await fetch("https://api.openai.com/v1/chat/completions", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${OPENAI_API_KEY}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          model: "gpt-4o-mini",
          messages: [
            ...completeMessages,
            responseMessage,
            {
              role: "tool",
              tool_call_id: toolCall.id,
              content: JSON.stringify(functionResult)
            }
          ],
          temperature: 0.7
        }),
      });

      const secondData = await secondResponse.json();
      result = {
        message: secondData.choices[0].message,
        functionCall: {
          name: functionName,
          arguments: functionArgs,
          result: functionResult
        }
      };
    }

    console.log("Received response from OpenAI");
    return new Response(JSON.stringify(result), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error("Error in chat-assistant function:", error);
    return new Response(JSON.stringify({
      message: {
        role: "assistant",
        content: "Sorry, I encountered an error while processing your request. Please try again later."
      },
      error: error.message
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      },
      status: 200 // Return 200 for graceful error handling on the frontend
    });
  }
});
// Helper function to detect if message is in Hinglish
function detectHinglish(message) {
  // Common Hindi words and patterns
  const hindiPatterns = [
    /kya/i,
    /hai/i,
    /kaise/i,
    /mein/i,
    /hum/i,
    /aap/i,
    /main/i,
    /nahi/i,
    /karenge/i,
    /milega/i,
    /chahiye/i,
    /kitna/i,
    /kab/i,
    /kaisa/i
  ];
  // Check if message contains any Hindi patterns
  return hindiPatterns.some((pattern)=>pattern.test(message));
}
// Helper function to identify booking-related queries
function containsBookingQuery(message) {
  const bookingKeywords = [
    /book/i,
    /booking/i,
    /slot/i,
    /reserve/i,
    /court/i,
    /time/i,
    /schedule/i,
    /appointment/i,
    /reservation/i,
    /meri booking/i,
    /admin bookings?/i,
    /venue bookings?/i,
    /show me all bookings/i
  ];
  return bookingKeywords.some((keyword)=>keyword.test(message));
}

// Helper function to identify availability-related queries
function containsAvailabilityQuery(message) {
  const availabilityKeywords = [
    /available/i,
    /availability/i,
    /free/i,
    /open/i,
    /slot/i,
    /time/i,
    /when/i,
    /kab/i,
    /available hai/i,
    /free hai/i,
    /khali hai/i,
    /book kar sakta/i,
    /book kar sakte/i,
    /available slots/i,
    /free slots/i,
    /what time/i,
    /kya time/i,
    /kitne baje/i
  ];
  return availabilityKeywords.some((keyword)=>keyword.test(message));
}
// Helper function to identify venue-related queries
function containsVenueQuery(message) {
  const venueKeywords = [
    /venue/i,
    /place/i,
    /ground/i,
    /court/i,
    /stadium/i,
    /arena/i,
    /location/i,
    /center/i,
    /centre/i,
    /where/i,
    /facility/i
  ];
  return venueKeywords.some((keyword)=>keyword.test(message));
}
// Helper function to identify sport-related queries
function containsSportQuery(message) {
  const sportKeywords = [
    /sport/i,
    /game/i,
    /play/i,
    /cricket/i,
    /football/i,
    /soccer/i,
    /basketball/i,
    /tennis/i,
    /badminton/i,
    /volleyball/i,
    /activity/i
  ];
  return sportKeywords.some((keyword)=>keyword.test(message));
}
// Helper function to detect challenge mode queries
function containsChallengeModeQuery(message) {
  if (!message) return false;
  const challengeKeywords = [
    /challenge mode/i,
    /challenge feature/i,
    /challenge section/i,
    /challenge kya hai/i,
    /challenge/i
  ];
  return challengeKeywords.some((pattern)=>pattern.test(message));
}
// Helper function to identify help/support/faq/privacy/contact queries
function containsSupportQuery(message) {
  if (!message) return false;
  const supportKeywords = [
    /help(\s|$)/i,
    /support(\s|$)/i,
    /faq(\s|$)/i,
    /frequently asked questions/i,
    /privacy(\s|$)/i,
    /privacy policy/i,
    /contact(\s|$)/i,
    /contact us/i,
    /samarthan/i,
    /madad/i,
    /sahayata/i,
    /niyam/i,
    /raabta/i,
    /kaise sampark kare/i,
    /kaise madad milegi/i,
    /kaise support milega/i,
    /kaise faq dekhu/i,
    /kaise privacy policy dekhu/i // How to see privacy policy
  ];
  return supportKeywords.some((pattern)=>pattern.test(message));
}
// Helper function to detect venue admin/owner contact queries
function containsVenueAdminContactQuery(message) {
  if (!message) return false;
  const patterns = [
    /contact.*(venue|ground|court).*(admin|owner|manager|incharge|person)/i,
    /how.*contact.*(venue|ground|court).*(admin|owner|manager|incharge|person)/i,
    /venue.*admin.*contact/i,
    /venue.*owner.*contact/i,
    /admin.*kaise/i,
    /owner.*kaise/i,
    /venue.*kaise contact/i,
    /kaise contact karu.*admin/i,
    /kaise baat karu.*admin/i,
    /venue.*admin.*baat/i,
    /venue.*admin.*message/i,
    /venue.*admin.*call/i,
    /venue.*owner.*baat/i,
    /venue.*owner.*call/i
  ];
  return patterns.some((pattern)=>pattern.test(message));
}
// Helper: Extract venue name from message
function extractVenueName(message, venueNames) {
  const lowerMsg = message.toLowerCase();
  return venueNames.find((name)=>lowerMsg.includes(name.toLowerCase()));
}
// Helper: Extract date from message (supports 'today', 'tomorrow', and formats like 'may 16')
function extractDateFromMessage(message) {
  const lowerMsg = message.toLowerCase();
  const today = new Date();
  if (lowerMsg.includes('today')) {
    return today.toISOString().split('T')[0];
  }
  if (lowerMsg.includes('tomorrow')) {
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);
    return tomorrow.toISOString().split('T')[0];
  }
  // Match formats like "may 16"
  const dateMatch = lowerMsg.match(/(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)[a-z]*\s+\d{1,2}/i);
  if (dateMatch) {
    const year = today.getFullYear();
    const dateStr = `${dateMatch[0]} ${year}`;
    const parsed = new Date(dateStr);
    if (!isNaN(parsed)) {
      return parsed.toISOString().split('T')[0];
    }
  }
  return null;
}
// Function to get user bookings using Supabase client
async function getUserBookings(userId) {
  try {
    console.log("Fetching bookings for user:", userId);
    // Get current date in YYYY-MM-DD format
    const today = new Date().toISOString().split('T')[0];
    // Get upcoming bookings with venue and court details
    const { data: upcomingBookings, error: upcomingError } = await supabase.from('bookings').select(`
        id,
        booking_date,
        start_time,
        end_time,
        total_price,
        status,
        courts:court_id (
          id,
          name,
          venue_id,
          venues:venue_id (
            id,
            name,
            location
          ),
          sports:sport_id (
            id,
            name
          )
        )
      `).eq('user_id', userId).gte('booking_date', today).order('booking_date', {
      ascending: true
    }).limit(5);
    if (upcomingError) throw upcomingError;
    // Get past bookings with venue and court details
    const { data: pastBookings, error: pastError } = await supabase.from('bookings').select(`
        id,
        booking_date,
        start_time,
        end_time,
        total_price,
        status,
        courts:court_id (
          id,
          name,
          venue_id,
          venues:venue_id (
            id,
            name,
            location
          ),
          sports:sport_id (
            id,
            name
          )
        )
      `).eq('user_id', userId).lt('booking_date', today).order('booking_date', {
      ascending: false
    }).limit(5);
    if (pastError) throw pastError;
    // Format bookings for better readability in assistant responses
    const formattedUpcoming = upcomingBookings?.map((booking)=>({
        date: booking.booking_date,
        time: `${booking.start_time} - ${booking.end_time}`,
        court: booking.courts?.name || 'Unknown court',
        venue: booking.courts?.venues?.name || 'Unknown venue',
        sport: booking.courts?.sports?.name || 'Unknown sport',
        status: booking.status,
        price: booking.total_price
      })) || [];
    const formattedPast = pastBookings?.map((booking)=>({
        date: booking.booking_date,
        time: `${booking.start_time} - ${booking.end_time}`,
        court: booking.courts?.name || 'Unknown court',
        venue: booking.courts?.venues?.name || 'Unknown venue',
        sport: booking.courts?.sports?.name || 'Unknown sport',
        status: booking.status,
        price: booking.total_price
      })) || [];
    return {
      success: true,
      upcoming: formattedUpcoming,
      past: formattedPast
    };
  } catch (error) {
    console.error("Error in getUserBookings:", error);
    return {
      success: false,
      error: error.message
    };
  }
}
// Function to get venues using Supabase client
async function getVenues() {
  try {
    const { data: venues, error } = await supabase.from('venues').select(`
        id,
        name,
        location,
        description,
        opening_hours,
        rating,
        sports:courts (
          sports:sport_id (
            id,
            name
          )
        )
      `).eq('is_active', true).order('name', {
      ascending: true
    });
    if (error) throw error;
    // Format venues for better readability
    const formattedVenues = venues?.map((venue)=>{
      // Extract unique sports from this venue
      const uniqueSports = new Set();
      venue.sports?.forEach((court)=>{
        if (court.sports?.name) {
          uniqueSports.add(court.sports.name);
        }
      });
      return {
        id: venue.id,
        name: venue.name,
        location: venue.location,
        description: venue.description,
        openingHours: venue.opening_hours,
        rating: venue.rating,
        sports: Array.from(uniqueSports)
      };
    }) || [];
    return {
      success: true,
      venues: formattedVenues
    };
  } catch (error) {
    console.error("Error in getVenues:", error);
    return {
      success: false,
      error: error.message
    };
  }
}
// Function to get sports using Supabase client
async function getSports() {
  try {
    const { data: sports, error } = await supabase.from('sports').select(`
        id,
        name,
        description
      `).eq('is_active', true).order('name', {
      ascending: true
    });
    if (error) throw error;
    return {
      success: true,
      sports: sports
    };
  } catch (error) {
    console.error("Error in getSports:", error);
    return {
      success: false,
      error: error.message
    };
  }
}
// Helper: Get venue IDs for which the user is an admin
async function getAdminVenueIds(userId) {
  const { data, error } = await supabase.from('venue_admins').select('venue_id').eq('user_id', userId);
  if (error) throw error;
  return data?.map((v)=>v.venue_id) || [];
}
// Helper: Check if user is an admin (has venues assigned)
async function isVenueAdmin(userId) {
  const venueIds = await getAdminVenueIds(userId);
  return venueIds.length > 0;
}
// Helper: Get all court IDs for a venue, grouped by court_group_id
async function getCourtIdsByGroupForVenue(venueId) {
  const { data: courts, error } = await supabase.from('courts').select('id, court_group_id').eq('venue_id', venueId).eq('is_active', true);
  if (error) throw error;
  // Group court IDs by court_group_id (null means not grouped)
  const groupMap = new Map();
  for (const court of courts){
    const groupId = court.court_group_id || court.id;
    if (!groupMap.has(groupId)) groupMap.set(groupId, []);
    groupMap.get(groupId).push(court.id);
  }
  return groupMap;
}
// Update getAdminBookings to aggregate by court_group_id
async function getAdminBookings(userId, filteredVenueIds = null, filterDate = null) {
  const venueIds = filteredVenueIds || await getAdminVenueIds(userId);
  if (!venueIds.length) return {
    success: true,
    upcoming: [],
    past: []
  };
  let allBookings = [];
  for (const venueId of venueIds){
    const groupMap = await getCourtIdsByGroupForVenue(venueId);
    for (const [groupId, courtIds] of groupMap.entries()){
      let query = supabase.from('bookings').select(`
          id, booking_date, start_time, end_time, total_price, status,
          courts:court_id (
            id, name, venue_id, venues:venue_id (id, name, location), sports:sport_id (id, name), court_group_id
          )
        `).in('court_id', courtIds).order('booking_date', {
        ascending: true
      });
      if (filterDate) {
        query = query.eq('booking_date', filterDate);
      }
      const { data: bookings, error } = await query;
      if (error) throw error;
      // For each group, only include unique bookings by time slot (avoid double-counting)
      // (Assume no overlapping bookings for the same group and time slot due to your triggers)
      allBookings.push(...bookings || []);
    }
  }
  // Format bookings for assistant
  const formatted = allBookings.map((booking)=>({
      date: booking.booking_date,
      time: `${booking.start_time} - ${booking.end_time}`,
      court: booking.courts?.name || 'Unknown court',
      venue: booking.courts?.venues?.name || 'Unknown venue',
      sport: booking.courts?.sports?.name || 'Unknown sport',
      status: booking.status,
      price: booking.total_price
    }));
  // Split into upcoming and past based on today
  const today = new Date().toISOString().split('T')[0];
  let upcoming = [], past = [];
  if (filterDate) {
    // If filtering by date, treat all as upcoming for that date
    upcoming = formatted;
  } else {
    upcoming = formatted.filter((b)=>b.date >= today);
    past = formatted.filter((b)=>b.date < today);
  }
  return {
    success: true,
    upcoming,
    past
  };
}

// Function to get available slots for a venue/court on a specific date
async function getAvailableSlots(supabase: any, venue_id: string, court_id: string | undefined, date: string) {
  try {
    let query;

    if (court_id) {
      // If court_id is provided, get slots for that specific court
      const { data, error } = await supabase
        .from('courts')
        .select(`
          id,
          name,
          venue:venues (
            id,
            name
          ),
          sport:sports (
            id,
            name
          )
        `)
        .eq('id', court_id);

      if (error) throw error;

      if (!data || data.length === 0) {
        return { success: false, message: "Court not found" };
      }

      const { data: slots, error: slotsError } = await supabase
        .rpc('get_unified_availability', {
          p_court_id: court_id,
          p_date: date
        });

      if (slotsError) throw slotsError;

      return {
        success: true,
        court: data[0],
        date: date,
        slots: slots || []
      };
    } else {
      // If no court_id, get all courts for the venue and their available slots
      const { data: venue, error: venueError } = await supabase
        .from('venues')
        .select(`
          id,
          name,
          courts (
            id,
            name,
            sport:sports (
              id,
              name
            )
          )
        `)
        .eq('id', venue_id)
        .eq('courts.is_active', true);

      if (venueError) throw venueError;

      if (!venue || venue.length === 0) {
        return { success: false, message: "Venue not found" };
      }

      const courts = venue[0].courts || [];

      if (venueError) throw venueError;

      const allSlots = [];

      // For each court, get available slots
      for (const court of courts) {
        const { data: slots, error: slotsError } = await supabase
          .rpc('get_unified_availability', {
            p_court_id: court.id,
            p_date: date
          });

        if (slotsError) throw slotsError;

        // Add available slots to result
        if (slots && slots.length > 0) {
          const availableSlots = slots.filter((slot: any) => slot.is_available);
          if (availableSlots.length > 0) {
            allSlots.push({
              court_id: court.id,
              court_name: court.name,
              sport_name: court.sport.name,
              slots: availableSlots
            });
          }
        }
      }

      return {
        success: true,
        venue: venue && venue.length > 0 ? venue[0] : null,
        date: date,
        courts_with_slots: allSlots
      };
    }
  } catch (error) {
    console.error("Error in getAvailableSlots:", error);
    return { success: false, message: "Failed to fetch available slots", error: error.message };
  }
}

// Function to get court availability details
async function getCourtAvailability(supabase: any, court_id: string, date: string) {
  try {
    // Get court details
    const { data: court, error: courtError } = await supabase
      .from('courts')
      .select(`
        id,
        name,
        venue:venues (
          id,
          name
        ),
        sport:sports (
          id,
          name
        ),
        hourly_rate
      `)
      .eq('id', court_id)
      .limit(1);

    if (courtError) throw courtError;

    if (!court || court.length === 0) {
      return { success: false, message: "Court not found" };
    }

    // Get template slots for the day using unified availability
    const { data: slots, error: slotsError } = await supabase
      .rpc('get_unified_availability', {
        p_court_id: court_id,
        p_date: date
      });

    if (slotsError) throw slotsError;

    // Get existing bookings for the court on the date
    const { data: bookings, error: bookingsError } = await supabase
      .from('bookings')
      .select(`
        id,
        start_time,
        end_time,
        status
      `)
      .eq('court_id', court_id)
      .eq('booking_date', date)
      .in('status', ['confirmed', 'pending']);

    if (bookingsError) throw bookingsError;

    return {
      success: true,
      court: court[0],
      date,
      availability: slots || [],
      bookings: bookings || []
    };
  } catch (error) {
    console.error("Error in getCourtAvailability:", error);
    return { success: false, message: "Failed to fetch court availability", error: error.message };
  }
}

// Function call handler for OpenAI function calling
async function handleFunctionCall(functionName: string, functionArgs: any, userId: string, supabase: any) {
  console.log(`=== EXECUTING FUNCTION: ${functionName} ===`);
  console.log(`Arguments:`, functionArgs);
  console.log(`User ID:`, userId);

  try {
    let result;
    switch (functionName) {
      case "get_unified_availability":
        console.log(`Calling getUnifiedAvailability with court_id: ${functionArgs.court_id}, date: ${functionArgs.date}`);
        result = await getUnifiedAvailability(supabase, functionArgs.court_id, functionArgs.date);
        break;
      case "search_courts_by_sport":
        console.log(`Calling searchCourtsBySport with sport: ${functionArgs.sport_name}, venue: ${functionArgs.venue_name}`);
        result = await searchCourtsBySport(supabase, functionArgs.sport_name, functionArgs.venue_name);
        break;
      case "prepare_booking_guidance":
        console.log(`Calling prepareBookingGuidance for court: ${functionArgs.court_id}`);
        result = await prepareBookingGuidance(supabase, userId, functionArgs.court_id, functionArgs.date, functionArgs.start_time, functionArgs.end_time);
        break;
      case "get_user_recent_bookings":
        console.log(`Calling getUserRecentBookings with limit: ${functionArgs.limit}, filter: ${functionArgs.status_filter}`);
        result = await getUserRecentBookings(supabase, userId, functionArgs.limit, functionArgs.status_filter);
        break;
      case "check_booking_status":
        console.log(`Calling checkBookingStatus with reference: ${functionArgs.booking_reference}`);
        result = await checkBookingStatus(supabase, userId, functionArgs.booking_reference);
        break;
      default:
        throw new Error(`Unknown function: ${functionName}`);
    }

    console.log(`=== FUNCTION ${functionName} COMPLETED ===`);
    console.log(`Result success:`, result?.success);
    console.log(`Result data length:`, result?.courts?.length || result?.slots?.length || result?.bookings?.length || 'N/A');

    return result;
  } catch (error) {
    console.error(`=== FUNCTION ${functionName} ERROR ===`);
    console.error(`Error:`, error);
    throw error;
  }
}

// Function to get unified availability using the RPC function
async function getUnifiedAvailability(supabase: any, court_id: string, date: string) {
  try {
    console.log(`Getting unified availability for court ${court_id} on ${date}`);

    // Get court details first
    const { data: court, error: courtError } = await supabase
      .from('courts')
      .select(`
        id,
        name,
        hourly_rate,
        venue:venues (
          id,
          name,
          location
        ),
        sport:sports (
          id,
          name,
          booking_type
        )
      `)
      .eq('id', court_id)
      .single();

    if (courtError) throw courtError;

    if (!court) {
      return {
        success: false,
        message: "Court not found"
      };
    }

    // Call the unified availability RPC function
    const { data: slots, error: slotsError } = await supabase
      .rpc('get_unified_availability', {
        p_court_id: court_id,
        p_date: date
      });

    if (slotsError) throw slotsError;

    return {
      success: true,
      court: court,
      date: date,
      slots: slots || [],
      total_available_slots: slots ? slots.filter((slot: any) => slot.is_available).length : 0
    };
  } catch (error) {
    console.error("Error in getUnifiedAvailability:", error);
    return {
      success: false,
      message: "Failed to fetch availability",
      error: error.message
    };
  }
}

// Function to search courts by sport and venue
async function searchCourtsBySport(supabase: any, sport_name: string, venue_name?: string) {
  try {
    console.log(`Searching courts for sport: ${sport_name}, venue: ${venue_name || 'any'}`);

    // First get sport ID by name
    const { data: sports, error: sportError } = await supabase
      .from('sports')
      .select('id, name')
      .ilike('name', `%${sport_name}%`)
      .limit(5);

    if (sportError) throw sportError;

    if (!sports || sports.length === 0) {
      return {
        success: false,
        message: `No sports found matching "${sport_name}". Available sports: Box Cricket, Box Football, Swimming, etc.`
      };
    }

    // Get sport IDs
    const sportIds = sports.map(sport => sport.id);

    let query = supabase
      .from('courts')
      .select(`
        id,
        name,
        hourly_rate,
        venue:venues (
          id,
          name,
          location
        ),
        sport:sports (
          id,
          name,
          booking_type
        )
      `)
      .eq('is_active', true)
      .in('sport_id', sportIds);

    // Filter by venue name if provided
    if (venue_name) {
      const { data: venues, error: venueError } = await supabase
        .from('venues')
        .select('id')
        .ilike('name', `%${venue_name}%`);

      if (venueError) throw venueError;

      if (venues && venues.length > 0) {
        const venueIds = venues.map(venue => venue.id);
        query = query.in('venue_id', venueIds);
      }
    }

    const { data: courts, error } = await query.limit(10);

    if (error) throw error;

    return {
      success: true,
      courts: courts || [],
      search_criteria: {
        sport_name,
        venue_name: venue_name || null
      }
    };
  } catch (error) {
    console.error("Error in searchCourtsBySport:", error);
    return {
      success: false,
      message: "Failed to search courts",
      error: error.message
    };
  }
}

// Function to prepare booking guidance (no payment processing)
async function prepareBookingGuidance(supabase: any, userId: string, court_id: string, date: string, start_time: string, end_time: string) {
  try {
    console.log(`Preparing booking guidance for user ${userId}, court ${court_id}, ${date} ${start_time}-${end_time}`);

    // First, verify the slot is still available
    const { data: availability, error: availError } = await supabase
      .rpc('get_unified_availability', {
        p_court_id: court_id,
        p_date: date
      });

    if (availError) throw availError;

    // Find the specific slot
    const requestedSlot = availability?.find((slot: any) =>
      slot.start_time === start_time && slot.end_time === end_time
    );

    if (!requestedSlot) {
      return {
        success: false,
        message: "The requested time slot was not found in the schedule."
      };
    }

    if (!requestedSlot.is_available) {
      return {
        success: false,
        message: "Sorry, this time slot is no longer available. Please choose another slot."
      };
    }

    // Get detailed court and venue information
    const { data: court, error: courtError } = await supabase
      .from('courts')
      .select(`
        id,
        name,
        hourly_rate,
        venue:venues (
          id,
          name,
          location,
          contact_number,
          amenities
        ),
        sport:sports (
          id,
          name,
          booking_type
        )
      `)
      .eq('id', court_id)
      .single();

    if (courtError) throw courtError;

    // Get user profile for personalization
    const { data: userProfile, error: userError } = await supabase
      .from('profiles')
      .select('full_name, email, phone')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error("Error fetching user profile:", userError);
    }

    // Calculate duration and total price
    const startParts = start_time.split(':').map(Number);
    const endParts = end_time.split(':').map(Number);
    const startMinutes = startParts[0] * 60 + startParts[1];
    const endMinutes = endParts[0] * 60 + endParts[1];
    const durationMinutes = endMinutes - startMinutes;
    const durationHours = durationMinutes / 60;

    // Use slot price if available, otherwise calculate from hourly rate
    const slotPrice = requestedSlot.price || (court.hourly_rate * durationHours);

    return {
      success: true,
      booking_type: "guidance", // This indicates it's guidance, not actual booking
      slot_details: {
        court_id: court_id,
        court_name: court.name,
        venue_name: court.venue.name,
        venue_location: court.venue.location,
        sport_name: court.sport.name,
        date: date,
        start_time: start_time,
        end_time: end_time,
        duration_minutes: durationMinutes,
        price: slotPrice,
        booking_type: court.sport.booking_type
      },
      user_details: {
        name: userProfile?.full_name || "User",
        email: userProfile?.email,
        phone: userProfile?.phone
      },
      next_steps: {
        action: "complete_booking",
        message: "Click 'Complete Booking' to proceed to payment and confirm your reservation.",
        payment_required: true
      }
    };
  } catch (error) {
    console.error("Error in prepareBookingGuidance:", error);
    return {
      success: false,
      message: "Failed to prepare booking guidance",
      error: error.message
    };
  }
}

// Function to get user's recent bookings
async function getUserRecentBookings(supabase: any, userId: string, limit: number = 10, statusFilter: string = 'all') {
  try {
    console.log(`Getting recent bookings for user ${userId}, limit: ${limit}, filter: ${statusFilter}`);

    // Get user profile for personalization
    const { data: userProfile, error: userError } = await supabase
      .from('profiles')
      .select('full_name, email')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error("Error fetching user profile:", userError);
    }

    const userName = userProfile?.full_name || "User";
    const today = new Date().toISOString().split('T')[0];

    let query = supabase
      .from('bookings')
      .select(`
        id,
        booking_reference,
        booking_date,
        start_time,
        end_time,
        status,
        total_price,
        payment_status,
        created_at,
        court:courts (
          id,
          name,
          venue:venues (
            id,
            name,
            location
          ),
          sport:sports (
            id,
            name
          )
        )
      `)
      .eq('user_id', userId);

    // Apply status filter
    if (statusFilter === 'upcoming') {
      query = query
        .in('status', ['confirmed', 'pending'])
        .gte('booking_date', today);
    } else if (statusFilter === 'past') {
      query = query
        .or('status.eq.completed,status.eq.cancelled')
        .lt('booking_date', today);
    }

    const { data: bookings, error } = await query
      .order('booking_date', { ascending: statusFilter === 'upcoming' })
      .order('start_time', { ascending: true })
      .limit(limit);

    if (error) throw error;

    // Separate upcoming and past bookings
    const upcoming = bookings?.filter(booking =>
      ['confirmed', 'pending'].includes(booking.status) &&
      booking.booking_date >= today
    ) || [];

    const past = bookings?.filter(booking =>
      ['completed', 'cancelled'].includes(booking.status) ||
      booking.booking_date < today
    ) || [];

    return {
      success: true,
      user_name: userName,
      total_bookings: bookings?.length || 0,
      upcoming_count: upcoming.length,
      past_count: past.length,
      bookings: {
        upcoming: upcoming,
        past: past,
        all: bookings || []
      },
      filter_applied: statusFilter
    };
  } catch (error) {
    console.error("Error in getUserRecentBookings:", error);
    return {
      success: false,
      message: "Failed to fetch recent bookings",
      error: error.message
    };
  }
}

// Function to check booking status
async function checkBookingStatus(supabase: any, userId: string, bookingReference: string) {
  try {
    console.log(`Checking booking status for reference: ${bookingReference}`);

    // Try to find booking by reference or ID
    let query = supabase
      .from('bookings')
      .select(`
        id,
        booking_reference,
        booking_date,
        start_time,
        end_time,
        status,
        total_price,
        payment_status,
        payment_method,
        created_at,
        updated_at,
        court:courts (
          id,
          name,
          venue:venues (
            id,
            name,
            location,
            contact_number
          ),
          sport:sports (
            id,
            name
          )
        )
      `)
      .eq('user_id', userId);

    // Check if it's a UUID (booking ID) or booking reference
    if (bookingReference.length === 36 && bookingReference.includes('-')) {
      query = query.eq('id', bookingReference);
    } else {
      query = query.eq('booking_reference', bookingReference);
    }

    const { data: booking, error } = await query.single();

    if (error) {
      if (error.code === 'PGRST116') {
        return {
          success: false,
          message: `No booking found with reference "${bookingReference}". Please check the reference number and try again.`
        };
      }
      throw error;
    }

    // Calculate time until booking
    const bookingDateTime = new Date(`${booking.booking_date}T${booking.start_time}`);
    const now = new Date();
    const timeDiff = bookingDateTime.getTime() - now.getTime();
    const hoursUntil = Math.round(timeDiff / (1000 * 60 * 60));

    let statusMessage = "";
    switch (booking.status) {
      case 'confirmed':
        if (hoursUntil > 0) {
          statusMessage = `Your booking is confirmed and starts in ${hoursUntil} hours.`;
        } else if (hoursUntil > -2) {
          statusMessage = "Your booking is currently active or just finished.";
        } else {
          statusMessage = "Your booking has been completed.";
        }
        break;
      case 'pending':
        statusMessage = "Your booking is pending confirmation. Payment may still be processing.";
        break;
      case 'cancelled':
        statusMessage = "This booking has been cancelled.";
        break;
      case 'completed':
        statusMessage = "This booking has been completed.";
        break;
      default:
        statusMessage = `Booking status: ${booking.status}`;
    }

    return {
      success: true,
      booking: booking,
      status_message: statusMessage,
      hours_until_booking: hoursUntil > 0 ? hoursUntil : null,
      can_cancel: booking.status === 'confirmed' && hoursUntil > 2, // Can cancel if more than 2 hours away
      payment_completed: booking.payment_status === 'completed'
    };
  } catch (error) {
    console.error("Error in checkBookingStatus:", error);
    return {
      success: false,
      message: "Failed to check booking status",
      error: error.message
    };
  }
}

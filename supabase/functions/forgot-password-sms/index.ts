import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client with service role
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Parse request body
    const { phone, otp, newPassword, action } = await req.json()

    if (!phone || !action) {
      return new Response(
        JSON.stringify({ error: 'Phone number and action are required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Clean and format phone number
    const cleanPhone = phone.replace(/[\s()-]/g, '')
    const formattedPhone = cleanPhone.startsWith('+91') ? cleanPhone : 
                          cleanPhone.startsWith('91') ? '+' + cleanPhone : 
                          '+91' + cleanPhone

    if (action === 'send-otp') {
      // Send OTP for password reset
      console.log('Sending SMS password reset OTP to:', formattedPhone)

      // Check if user exists and is verified
      const { data: profile, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('id, phone_verified, full_name')
        .eq('phone', formattedPhone)
        .eq('phone_verified', true)
        .single()

      if (profileError || !profile) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Phone number not found or not verified. Please register first.'
          }),
          {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Use SMS OTP system for password reset
      const { data, error } = await supabaseAdmin.functions.invoke('send-sms-otp', {
        body: {
          phone: formattedPhone,
          fullName: profile.full_name,
          purpose: 'password_reset'
        }
      })

      if (error || !data.success) {
        console.error('Failed to send SMS OTP:', error || data.error)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Failed to send SMS OTP'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      return new Response(
        JSON.stringify({
          success: true,
          message: 'SMS OTP sent successfully'
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )

    } else if (action === 'verify-otp') {
      // Verify OTP for password reset
      if (!otp) {
        return new Response(
          JSON.stringify({ error: 'OTP is required for verification' }),
          { 
            status: 400, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }

      console.log('Verifying SMS password reset OTP for:', formattedPhone)

      // Validate OTP using optimized database function
      const { data: validationResult, error: validationError } = await supabaseAdmin
        .rpc('validate_password_reset_otp', {
          phone_number: formattedPhone,
          otp_input: otp
        })

      if (validationError) {
        console.error('Password reset OTP validation error:', validationError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'OTP validation failed'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      if (!validationResult.is_valid) {
        console.log('Invalid password reset OTP for phone:', formattedPhone, 'Error:', validationResult.error_message)
        return new Response(
          JSON.stringify({
            success: false,
            error: validationResult.error_message || 'Invalid or expired OTP'
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      return new Response(
        JSON.stringify({
          success: true,
          message: 'OTP verified successfully. You can now reset your password.',
          phone: formattedPhone
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )

    } else if (action === 'reset-password') {
      // Reset password after OTP verification
      if (!otp || !newPassword) {
        return new Response(
          JSON.stringify({ error: 'OTP and new password are required' }),
          { 
            status: 400, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }

      console.log('Resetting password for:', formattedPhone)

      // Verify OTP again for security using optimized database function
      const { data: validationResult, error: validationError } = await supabaseAdmin
        .rpc('validate_password_reset_otp', {
          phone_number: formattedPhone,
          otp_input: otp
        })

      if (validationError || !validationResult.is_valid) {
        console.error('Password reset OTP validation error during reset:', validationError || validationResult.error_message)
        return new Response(
          JSON.stringify({
            success: false,
            error: validationResult?.error_message || 'Invalid or expired OTP'
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Get user profile
      const { data: profile, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('id')
        .eq('phone', formattedPhone)
        .eq('phone_verified', true)
        .single()

      if (profileError || !profile) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'User profile not found'
          }),
          {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Update password using Supabase Auth Admin API
      const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(
        profile.id,
        { password: newPassword }
      )

      if (updateError) {
        console.error('Password update error:', updateError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Failed to update password'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      console.log('Password updated successfully for user:', profile.id)

      // Clean up pending SMS OTP record
      const { error: cleanupError } = await supabaseAdmin
        .from('pending_whatsapp_users')
        .delete()
        .eq('phone', formattedPhone)
        .eq('purpose', 'password_reset')

      if (cleanupError) {
        console.error('Error cleaning up pending SMS OTP:', cleanupError)
        // Don't fail the request, cleanup is not critical
      }

      return new Response(
        JSON.stringify({
          success: true,
          message: 'Password reset successfully'
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )

    } else {
      return new Response(
        JSON.stringify({ error: 'Invalid action. Must be "send-otp", "verify-otp", or "reset-password"' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }
  } catch (error) {
    console.error('Error in forgot-password-sms function:', error)
    return new Response(
      JSON.stringify({ 
        success: false,
        error: 'Internal server error',
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

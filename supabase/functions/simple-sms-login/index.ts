import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client with service role
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Parse request body
    const { phone, otp, action } = await req.json()

    if (!phone || !action) {
      return new Response(
        JSON.stringify({ error: 'Phone number and action are required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Clean and format phone number
    const cleanPhone = phone.replace(/[\s()-]/g, '')
    const formattedPhone = cleanPhone.startsWith('+91') ? cleanPhone : 
                          cleanPhone.startsWith('91') ? '+' + cleanPhone : 
                          '+91' + cleanPhone

    if (action === 'send') {
      // Send OTP for login
      console.log('Sending SMS login OTP to:', formattedPhone)

      // Check if user exists and is verified
      const { data: profile, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('id, phone_verified')
        .eq('phone', formattedPhone)
        .eq('phone_verified', true)
        .single()

      if (profileError || !profile) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Phone number not found or not verified. Please register first.'
          }),
          {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Use SMS OTP system for login
      const { data, error } = await supabaseAdmin.functions.invoke('send-sms-otp', {
        body: {
          phone: formattedPhone,
          purpose: 'login',
          isLogin: true
        }
      })

      if (error || !data.success) {
        console.error('Failed to send SMS OTP:', error || data.error)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Failed to send SMS OTP'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      return new Response(
        JSON.stringify({
          success: true,
          message: 'SMS OTP sent successfully'
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    } else if (action === 'verify') {
      // Verify OTP and login
      if (!otp) {
        return new Response(
          JSON.stringify({ error: 'OTP is required for verification' }),
          { 
            status: 400, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
      }

      console.log('Verifying SMS login OTP for:', formattedPhone)

      // Validate OTP using database function
      const { data: validationResult, error: validationError } = await supabaseAdmin
        .rpc('validate_whatsapp_otp', { 
          phone_number: formattedPhone, 
          otp_input: otp 
        })

      if (validationError) {
        console.error('OTP validation error:', validationError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'OTP validation failed'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      if (!validationResult.is_valid) {
        return new Response(
          JSON.stringify({
            success: false,
            error: validationResult.error_message || 'Invalid or expired OTP'
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Get user profile for login
      const { data: profile, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('id, phone, full_name, email')
        .eq('phone', formattedPhone)
        .eq('phone_verified', true)
        .single()

      if (profileError || !profile) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'User profile not found'
          }),
          {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Generate session for the user
      const { data: sessionData, error: sessionError } = await supabaseAdmin.auth.admin.generateLink({
        type: 'magiclink',
        email: profile.email,
        options: {
          redirectTo: `${Deno.env.get('SUPABASE_URL')}/auth/v1/callback`
        }
      })

      if (sessionError) {
        console.error('Error generating session:', sessionError)
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Failed to create user session'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      // Clean up pending SMS OTP record
      const { error: cleanupError } = await supabaseAdmin
        .from('pending_whatsapp_users')
        .delete()
        .eq('phone', formattedPhone)
        .eq('purpose', 'login')

      if (cleanupError) {
        console.error('Error cleaning up pending SMS OTP:', cleanupError)
        // Don't fail the request, cleanup is not critical
      }

      return new Response(
        JSON.stringify({
          success: true,
          message: 'SMS OTP verified successfully',
          user: {
            id: profile.id,
            phone: profile.phone,
            full_name: profile.full_name,
            email: profile.email
          },
          sessionUrl: sessionData.properties?.action_link
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    } else {
      return new Response(
        JSON.stringify({ error: 'Invalid action. Must be "send" or "verify"' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }
  } catch (error) {
    console.error('Error in simple-sms-login function:', error)
    return new Response(
      JSON.stringify({ 
        success: false,
        error: 'Internal server error',
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

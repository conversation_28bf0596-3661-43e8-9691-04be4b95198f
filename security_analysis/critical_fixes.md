# Critical Security Fixes - Immediate Action Required

## 🚨 CRITICAL ISSUE 1: Exposed Supabase Keys

### Current Problem
The Supabase URL and anon key are hardcoded in the frontend code, making them visible to anyone who inspects the application.

### File to Fix: `src/integrations/supabase/client.ts`

### Current Code (INSECURE):
```typescript
const SUPABASE_URL = "https://lrtirloetmulgmdxnusl.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxydGlybG9ldG11bGdtZHhudXNsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4NzAxODYsImV4cCI6MjA2MTQ0NjE4Nn0.a1PkDU3cFd_BWNtf76m5a_NM5JCc9gO5xgN_tzJpquM";
```

### Secure Code (RECOMMENDED):
```typescript
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL;
const SUPABASE_PUBLISHABLE_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!SUPABASE_URL || !SUPABASE_PUBLISHABLE_KEY) {
  throw new Error('Missing required Supabase environment variables');
}
```

### Environment Variables Setup
Create `.env.local` file in project root:
```env
VITE_SUPABASE_URL=https://lrtirloetmulgmdxnusl.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxydGlybG9ldG11bGdtZHhudXNsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4NzAxODYsImV4cCI6MjA2MTQ0NjE4Nn0.a1PkDU3cFd_BWNtf76m5a_NM5JCc9gO5xgN_tzJpquM
```

### Additional Security Measures
1. **Verify RLS Policies**: Ensure all database tables have proper Row Level Security policies
2. **Rotate Keys**: Consider rotating the anon key if it has been exposed
3. **Monitor Usage**: Set up monitoring for unusual API usage patterns

---

## 🚨 CRITICAL ISSUE 2: Overly Permissive CORS

### Current Problem
All Edge Functions use wildcard CORS (`Access-Control-Allow-Origin: *`), allowing requests from any domain.

### Files to Fix:
- `supabase/functions/weather-proxy/index.ts`
- `supabase/functions/phone-password-login/index.ts`
- `supabase/functions/simple-whatsapp-login/index.ts`
- `supabase/functions/send-msg91-email/index.ts`
- `supabase/functions/verify-whatsapp-email-token/index.ts`
- `supabase/functions/razorpay-webhook/index.ts`
- And all other Edge Functions

### Current Code (INSECURE):
```typescript
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}
```

### Secure Code (RECOMMENDED):
```typescript
const corsHeaders = {
  'Access-Control-Allow-Origin': Deno.env.get('ENVIRONMENT') === 'production' 
    ? 'https://grid2play.com' 
    : '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Max-Age': '86400',
  'Access-Control-Allow-Credentials': 'true'
};
```

### Environment Variable for Production
Add to Supabase Edge Function environment:
```env
ENVIRONMENT=production
ALLOWED_ORIGINS=https://grid2play.com,https://www.grid2play.com
```

---

## 🚨 CRITICAL ISSUE 3: Missing Security Headers

### Current Problem
Security headers are defined but not consistently applied across the application.

### Solution: Create Security Middleware

Create `src/utils/security-middleware.ts`:
```typescript
export const getSecurityHeaders = () => {
  const headers: Record<string, string> = {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()'
  };

  // Add HSTS only in production
  if (import.meta.env.PROD) {
    headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains; preload';
  }

  return headers;
};

export const applySecurityHeaders = (response: Response): Response => {
  const securityHeaders = getSecurityHeaders();
  const headers = new Headers(response.headers);
  
  Object.entries(securityHeaders).forEach(([key, value]) => {
    headers.set(key, value);
  });
  
  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers
  });
};
```

### Apply to Edge Functions
Update all Edge Functions to use security headers:
```typescript
import { applySecurityHeaders } from '../_shared/security-middleware.ts';

// At the end of each function
return applySecurityHeaders(new Response(
  JSON.stringify(result),
  { 
    status: 200,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  }
));
```

---

## Implementation Checklist

### Immediate Actions (Next 2 Hours)
- [ ] Move Supabase keys to environment variables
- [ ] Update CORS configuration for production
- [ ] Test application with new environment variables
- [ ] Verify RLS policies are working correctly

### Short Term (Next 24 Hours)
- [ ] Implement security headers middleware
- [ ] Update all Edge Functions with secure CORS
- [ ] Set up environment-specific configurations
- [ ] Test all authentication flows

### Validation Steps
1. **Environment Variables**: Verify app works with env vars
2. **CORS Testing**: Test from different domains
3. **Security Headers**: Use online security scanners
4. **RLS Testing**: Verify users can only access their data

### Monitoring Setup
1. Set up alerts for failed authentication attempts
2. Monitor API usage patterns for anomalies
3. Regular security scans of the application
4. Log analysis for suspicious activities

---

**URGENT**: These fixes should be implemented immediately to prevent potential security breaches.
**Timeline**: Complete within 24 hours
**Testing**: Thoroughly test all authentication and API flows after implementation
